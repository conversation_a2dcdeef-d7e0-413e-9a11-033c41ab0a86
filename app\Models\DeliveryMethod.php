<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class DeliveryMethod extends Model
{
    use HasFactory,SoftDeletes;

    protected $table = 'delivery_methods';

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(
            DeliveryMethod::class,
            'delivery_method_products',
            'delivery_method_id',
            'products_id'
        );
    }
    public function orders(): Has<PERSON>any
    {
        return $this->hasMany(order::class);
    }
}
