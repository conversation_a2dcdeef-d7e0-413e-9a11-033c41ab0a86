<?php

namespace App\Services\BasalamService\Review;

use App\Repositories\BasalamRepository\BasalamReviewRepository as ReviewRepo;
use App\Repositories\BasalamRepository\BasalamUserRepository as UserRepo;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use InvalidArgumentException;

class ReviewService
{

    /**
     * The repository instance used for managing Basalam review counts.
     *
     * @var ReviewRepo
     */
    private ReviewRepo $reviewRepository;

    private UserRepo $userRepository;


    /**
     * Constructs a new instance of the ReviewService.
     *
     * @param ReviewRepo $reviewRepository The repository instance for review operations.
     */
    public function __construct(ReviewRepo $reviewRepository, UserRepo $userRepository)
    {
        $this->reviewRepository = $reviewRepository;
        $this->userRepository = $userRepository;
    }

    /**
     * Validates a given review payload.
     *
     * @param string $payload The review payload as a JSON string.
     * @return array The validated payload data.
     * @throws InvalidArgumentException if the payload is invalid.
     */
    public function validateReviewPayload(string $payload): array
    {
        $decodedPayload = json_decode($payload, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new InvalidArgumentException('Invalid JSON payload.');
        }

        $rules = [
            'message_id' => 'required|integer|exists:basalam_messages,id',
            'score' => ['required', 'integer', Rule::in([1, 2, 3])],
        ];

        $validator = Validator::make($decodedPayload, $rules);

        if ($validator->fails()) {
            throw new InvalidArgumentException($validator->errors()->first());
        }

        return $decodedPayload;
    }

    /**
     * Creates a new review instance using the validated payload.
     *
     * @param string $recipientId
     * @param string $reviewJson The review payload as a JSON string.
     * @return void
     */
    public function createReview(string $recipientId, string $reviewJson): void
    {
        $validatedData = $this->validateReviewPayload($reviewJson);
        $user = $this->userRepository->getUserByInstagramId($recipientId);

        $this->reviewRepository->create([
            'user_id'    => $user->id,
            'message_id' => $validatedData['message_id'],
            'score'      => $validatedData['score'],
        ]);
    }
}
