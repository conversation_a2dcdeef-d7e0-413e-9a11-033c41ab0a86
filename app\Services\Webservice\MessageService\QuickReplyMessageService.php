<?php

namespace App\Services\Webservice\MessageService;

use App\Services\Webservice\Abstract\AbstractMessageService;

class QuickReplyMessageService extends AbstractMessageService
{
	protected string $attachmentType = 'template';

	/**
	 * Gets the message content for a text message.
	 *
	 * @param array $messageData
	 * @return array
	 */
	protected function getMessageContent(array $messageData): array
	{
		$quickReplies = [];

		foreach ($messageData['quick_replies'] as $quickReply) {
			$quickReplies[] = [
				'content_type' => 'text',
				'title'        => $quickReply['title'],
				'payload'      => $quickReply['payload'],
			];
		}

		return [
			'text'          => $messageData['text'],
			'quick_replies' => $quickReplies,
		];
	}
}
