<?php

namespace App\Http\Controllers\Basalam;

use App\Http\Controllers\Controller;
use App\Models\Basalam\BasalamBrand;
use App\Models\Basalam\BasalamCategory;
use App\Services\BasalamService\Enums\WebhookTypeEnum;
use App\Services\BasalamService\ImageService;
use App\Services\BasalamService\Messengers\MessageService;
use App\Services\BasalamService\Messengers\PostbackService;
use App\Services\BasalamService\Product\ProductProcessorService;
use App\Services\BasalamService\TextService;
use App\Services\BasalamService\User\UserService;
use App\Services\BasalamService\Webhook\WebhookHandler;
use App\Services\TelegramService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class MainController extends Controller
{
	private UserService $userService;
	private PostbackService $postbackService;
	private WebhookHandler $webhookHandler;
	private ProductProcessorService $productProcessor;
	private MessageService $messageService;
	private ImageService $imageService;
	private TextService $textService;

	public function __construct(
		UserService             $userService,
		PostbackService         $postbackService,
		WebhookHandler          $webhookHandler,
		ProductProcessorService $productProcessor,
		MessageService          $messageService,
		ImageService            $imageService,
		TextService             $textService,
	)
	{
		$this->webhookHandler = $webhookHandler;
		$this->userService = $userService;
		$this->postbackService = $postbackService;
		$this->productProcessor = $productProcessor;
		$this->messageService = $messageService;
		$this->imageService = $imageService;
		$this->textService = $textService;
	}

	public function handle(Request $request): void
	{
		if ($this->webhookHandler->isEcho($request)) {
			exit();
		}

		if ($this->webhookHandler->isRead($request)) {
			exit();
		}

		if ($this->webhookHandler->isPostback($request)) {
			$this->postbackService->handle($request);
			exit();
		}

		$senderId = $this->webhookHandler->getSenderId($request);
		$this->userService->incrementMessageCount($senderId);

		$this->webhookHandler->extractWebhook($request);
		$webhookData = $this->webhookHandler->webhookData;
		$webhookType = $this->webhookHandler->webhookType;

		try {
			if ($this->userService->isNewUser($senderId))
				$this->userService->handleNewUser($senderId);

			// AI image search
			$supportedMediaTypes = [
				WebhookTypeEnum::IMAGE->value,
				WebhookTypeEnum::SHARE->value,
			];
			if (in_array($webhookType, $supportedMediaTypes)) {
				$messageId = $this->userService->addImageMessage($senderId, $webhookData);
				$product = $this->imageService->getProductFromImageUrl($webhookData);

				if ($product !== null) {
					$category = BasalamCategory::query()
						->where('title', $product->category)
						->first();

					$brand = null;
					if (Str::of($product->brand)->isNotEmpty()) {
						$brand = BasalamBrand::query()->firstOrCreate([
							'title' => $product->brand,
						], [
							'category_id' => $category?->id,
						]);
					}

					$this->userService->updateMessage($messageId, [
						'category_id' => $category?->id,
						'brand_id'    => $brand?->id,
						'query'       => $product->name,
					]);
				}

				// TODO: remove
				TelegramService::sendForHamid($product);
				TelegramService::sendForHamid($webhookData);

				$this->webhookHandler->sendWaitingMessageForImage($senderId);
				$isBaSalamProcessed = $this->productProcessor->processImageBaSalam($senderId, $webhookData);
				$isTorobProcessed = $this->productProcessor->processImageTorob($senderId, $webhookData);

				if ($isBaSalamProcessed || $isTorobProcessed) {
					$this->userService->updateMessage($messageId, [
						'is_processed' => true,
					]);
					$this->productProcessor->sendReviewButton($senderId, $messageId);
				} else {
					$this->messageService->sendTextMessage(
						$senderId,
						'فعلا سیستم در حال آپدیته… به محض اینکه درست شد محصولاتو برات میفرستیم 🙏',
					);
				}

				exit();
			}

			// AI text search
			if ($webhookType === WebhookTypeEnum::TEXT->value) {
				$messageId = $this->userService->addTextMessage($senderId, $webhookData);
				$product = $this->textService->getProductFromTextQuery($webhookData);
				$this->webhookHandler->sendWaitingMessageForText($senderId, $product);
				$isBaSalamProcessed = $this->productProcessor->processTextBaSalam($senderId, $product);
				$isTorobProcessed = $this->productProcessor->processTextTorob($senderId, $product);

				if ($isBaSalamProcessed || $isTorobProcessed) {
					$this->userService->updateMessage($messageId, [
						'is_processed' => true,
					]);
					$this->productProcessor->sendReviewButton($senderId, $messageId);
				} else {
					$this->messageService->sendTextMessage(
						$senderId,
						'فعلا سیستم در حال آپدیته… به محض اینکه درست شد محصولاتو برات میفرستیم 🙏',
					);
				}

				exit();
			}

			$this->webhookHandler->handleInvalidFormatMessage($senderId);
		} catch (\Exception $e) {
			TelegramService::sendForHamid($e->getTrace());
		}
	}
}
