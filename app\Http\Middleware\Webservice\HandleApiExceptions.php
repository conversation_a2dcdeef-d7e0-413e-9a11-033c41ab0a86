<?php

namespace App\Http\Middleware\Webservice;

use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Exceptions\Webservice\InvalidApiTokenException;
use App\Exceptions\Webservice\InvalidEndpointException;
use App\Exceptions\Webservice\InvalidMessageTypeException;

class HandleApiExceptions
{
    /**
     * Handles an incoming Webservice request and catch specific exceptions.
     *
     * @param Request $request
     * @param Closure $next
     * @return JsonResponse|mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {
        try {
            return $next($request);

        } catch (InvalidApiTokenException | InvalidEndpointException | InvalidMessageTypeException $e) {
            return $e->render($request);

        } catch (\Exception $e) {
            return response()->json([
                'error'   => 'An unexpected error occurred.',
                'details' => $e->getMessage(),
            ], status: 500);
        }
    }
}
