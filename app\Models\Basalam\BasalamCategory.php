<?php

namespace App\Models\Basalam;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class BasalamCategory extends Model
{
	protected $table = 'basalam_categories';
	public $timestamps = false;

	protected $fillable = [
		'parent_id',
		'title',
	];

	public function parent(): BelongsTo
	{
		return $this->belongsTo(BasalamCategory::class, 'parent_id');
	}

	public function children(): HasMany
	{
		return $this->hasMany(BasalamCategory::class, 'parent_id');
	}

	public function brands(): HasMany
	{
		return $this->hasMany(BasalamBrand::class);
	}

	public function messages(): HasMany
	{
		return $this->hasMany(BasalamMessage::class);
	}
}
