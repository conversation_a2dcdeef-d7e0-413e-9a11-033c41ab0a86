<?php

namespace App\Services\BasalamService;

use App\Models\Basalam\BasalamCategory;
use Exception;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ImageService
{
	public function getProductFromImageUrl(string $imageUrl): ?object
	{
		try {
			$categories = BasalamCategory::query()
				->whereNotNull('parent_id')
				->orWhere('title', 'سایر')
				->get()
				->map(fn ($category) => $category->title)
				->join(',');
			$prompt = <<<EOT
You are a genius in all kinds of commercial products.
Extract name, brand and category for each image url.
Follow these rules:
1. Include all image urls in the response.
2. Only use these values for category ($categories)
3. Generate responses in farsi/persian language.
4. If product is unknown set success to false.
5. If brand is unknown set it to "".
EOT;
			$baseUrl = config('services.open_ai.base_url');
			$headers = [
				'Content-Type'  => 'application/json',
				'Authorization' => 'Bearer '.config('services.open_ai.api_key'),
			];
			$payload = [
				'model'           => 'gpt-4o-2024-08-06',
				'max_tokens'      => 2000,
				'store'           => true,
				'messages'        => [
					[
						'role'    => 'system',
						'content' => $prompt,
					],
					[
						'role'    => 'user',
						'content' => [
							[
								'type'      => 'image_url',
								'image_url' => [
									'url' => $imageUrl,
								],
							],
						],
					],
				],
				'response_format' => [
					'type'        => 'json_schema',
					'json_schema' => [
						'name'   => 'products_response',
						'schema' => [
							'type'                 => 'object',
							'properties'           => [
								'success' => ['type' => 'boolean'],
								'product' => [
									'type'                 => 'object',
									'properties'           => [
										'name'     => ['type' => 'string'],
										'brand'    => ['type' => 'string'],
										'category' => ['type' => 'string'],
									],
									'required'             => ['name', 'brand', 'category'],
									'additionalProperties' => false,
								],
							],
							'required'             => ['success', 'product'],
							'additionalProperties' => false,
						],
						'strict' => true,
					],
				],
			];

			$response = Http::timeout(20)
				->withHeaders($headers)
				->post($baseUrl.'/completions', $payload);

			if ($response->successful()) {
				$result = $response->json('choices.0.message.content');
				$result = Json::decode($result, false);

				if ($result->success)
					return $result->product;
			}

			return null;
		} catch (ConnectionException $e) {
			Log::error("***************** OPEN AI ******************");
			Log::error($e->getMessage());

			return null;
		}
	}

	/**
	 * Processes the picture: Saves it from a URL and send it to a controller.
	 *
	 * @param string $url The URL of the picture to save.
	 * @param string $endpoint The URL of the endpoint to send the picture.
	 * @return string The result of the operation.
	 * @throws Exception If the picture sending or deletion fails.
	 */
	public function processImageBaSalam(string $url, string $endpoint): string
	{
		try {
			$filePath = $this->savePictureFromUrl($url);

			return $this->sendPictureToController($filePath, $endpoint);
		} catch (Exception $e) {
			throw new Exception("Error processing picture: ".$e->getMessage());
		}
	}

	public function processImageTorob(string $url, string $endpoint): ?string
	{
		try {
			$filePath = $this->savePictureFromUrl($url);

			return $this->sendPictureToTorob($filePath, $endpoint);
		} catch (Exception $e) {
			Log::error($e->getMessage());

			return null;
		}
	}

	/**
	 * Saves picture from a URL to storage.
	 *
	 * @param string $url The URL of the picture to save.
	 * @return string|null The saved picture's file path.
	 * @throws Exception If the download or save fails.
	 */
	private function savePictureFromUrl(string $url): ?string
	{
		try {
			$response = Http::timeout(20)->get($url);

			if ($response->successful()) {
				$fileName = 'image_'.time().'.jpg';

				Storage::disk('public')->put($fileName, $response->body());

				return $fileName;
			}

			return null;
		} catch (Exception $e) {
			Log::error($e->getMessage());

			return null;
		}
	}

	/**
	 * Send a picture to a controller endpoint and delete it after successful submission.
	 *
	 * @param string $filePath The file path of the picture.
	 * @param string $endpoint The URL of the endpoint to send the picture.
	 * @return string|null The server's response message.
	 * @throws Exception If the picture sending or deletion fails.
	 */
	private function sendPictureToController(string $filePath, string $endpoint): ?string
	{
		$absolutePath = storage_path('app/public/'.$filePath);

		if (!file_exists($absolutePath))
			return null;

		try {
			$fileContent = file_get_contents($absolutePath);

			$response = Http::timeout(20)
				->attach('image', $fileContent, basename($absolutePath))
				->post($endpoint);

			if ($response->successful()) {
				$this->deletePicture($filePath);
				return $response->body();
			}

			return null;
		} catch (Exception $e) {
			Log::error($e->getMessage());

			return null;
		}
	}

	private function sendPictureToTorob(string $filePath, string $endpoint): ?string
	{
		$absolutePath = storage_path('app/public/'.$filePath);

		if (!file_exists($absolutePath))
			return null;

		try {
			$fileContent = file_get_contents($absolutePath);

			$response = Http::timeout(20)
				->attach('file', $fileContent, basename($absolutePath))
				->post($endpoint);

			if ($response->successful()) {
				$this->deletePicture($filePath);
				return $response->body();
			}

			return null;
		} catch (Exception $e) {
			Log::error($e->getMessage());

			return null;
		}
	}

	/**
	 * Removes the Post Picture, and it's path from storage.
	 *
	 * @param string $filePath Path of the Post Picture to be removed.
	 * @return void
	 */
	private function deletePicture(string $filePath): void
	{
		Storage::delete($filePath);
		Storage::disk('public')->delete($filePath);
	}
}
