<?php

namespace App\Console\Commands\Basalam;

use App\Repositories\BasalamRepository\BasalamUserRepository;
use App\Services\BasalamService\Messengers\MessageService;
use App\Services\BasalamService\Product\ProductProcessorService;
use App\Services\BasalamService\User\UserService;
use App\Services\TelegramService;
use Illuminate\Console\Command;

class HandleUnprocessedMessages extends Command
{
	private BasalamUserRepository $userRepository;
	private UserService $userService;
	private ProductProcessorService $productProcessorService;
	private MessageService $messageService;

	public function __construct(
		BasalamUserRepository   $userRepository,
		UserService             $userService,
		ProductProcessorService $productProcessorService,
		MessageService          $messageService,
	)
	{
		parent::__construct();
		$this->userRepository = $userRepository;
		$this->userService = $userService;
		$this->productProcessorService = $productProcessorService;
		$this->messageService = $messageService;
	}

	/**
	 * The name and signature of the console command.
	 *
	 * @var string
	 */
	protected $signature = 'chijost:handle-unprocessed-messages';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Handles unprocessed messages in the last 24 hours';

	/**
	 * Execute the console command.
	 */
	public function handle()
	{
		$users = $this->userRepository->getUsersWithLatestUnprocessedImageMessage()
			->select([
				'id',
				'instagram_id',
			])
//			->limit(5)
			->get();

		// TODO: remove
		TelegramService::sendForHamid([
			'Command' => 'HandleUnprocessedMessages',
			'Users'   => $users->count(),
		]);

		foreach ($users as $user) {
			$message = $user->messages->last();
			$this->messageService->sendTextMessage(
				$user->instagram_id,
				'آپدیت سیستم تموم شد داریم دنبال دقیق‌ترین نتایج برات میگردیم، یکمی صبر کنی ارسال میشه 😉',
			);
			$isBaSalamProcessed = $this->productProcessorService->processImageBaSalam($user->instagram_id, $message->url);
			$isTorobProcessed = $this->productProcessorService->processImageTorob($user->instagram_id, $message->url);

			if ($isBaSalamProcessed || $isTorobProcessed) {
				$this->userService->updateMessage($message->id, [
					'is_processed' => true,
				]);
				$this->productProcessorService->sendReviewButton($user->instagram_id, $message->id);
			}
		}
	}
}
