<?php

use App\Http\Controllers\Api\V1\ManyMessageAgentsController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Webhook\InstagramWebhook\InstagramWebhookController as InstaWebhook;
// use App\Http\Controllers\WheelOfFortune\DiscountController;
require base_path('routes/webservice.php');
require base_path('routes/basalam.php');

Route::match(['get', 'post'], 'instagram-webhook', [InstaWebhook::class, 'handle']);
Route::match(['get', 'post'], 'paymentCallbackL', [InstaWebhook::class, 'paymentCallbackL']);
Route::post('paymentCallback', [InstaWebhook::class, 'paymentCallback']);
Route::get('redirectPayment', [InstaWebhook::class, 'redirectPayment']);


//test for matin AI (we should delete this later)
// Route::post('/send-discount', [DiscountController::class, 'sendDiscount']);

/*
|--------------------------------------------------------------------------
| ManyMessage Agent Routes
|--------------------------------------------------------------------------
*/

// Or apply to a group of routes
Route::middleware('api.key')->group(function () {
    Route::resource('/manymessage-agent', ManyMessageAgentsController::class)->only(['store']);
});
