<?php

namespace App\Services\BasalamService\User;

use App\Models\Basalam\BasalamUser;
use App\Repositories\BasalamRepository\BasalamMessageRepository;
use App\Repositories\BasalamRepository\BasalamUserRepository;
use App\Services\BasalamService\Messengers\MessageService;

class UserService
{
	private BasalamUserRepository $userRepository;
	private BasalamMessageRepository $messageRepository;
	private MessageService $messageService;

	public function __construct(
		BasalamUserRepository    $userRepository,
		BasalamMessageRepository $messageRepository,
		MessageService           $messageService,
	)
	{
		$this->userRepository = $userRepository;
		$this->messageRepository = $messageRepository;
		$this->messageService = $messageService;
	}

	public function isNewUser($instagramId): bool
	{
		return !BasalamUser::hasInstagramId($instagramId);
	}

	public function handleNewUser(string $senderId): void
	{
		$this->userRepository->createUser($senderId);

		$this->sendWelcomeMessage($senderId);
	}

	public function incrementMessageCount(string $senderId): void
	{
		$this->userRepository->incrementMessageCount($senderId);
	}

	private function sendWelcomeMessage(string $senderId): void
	{
		$text = "دوست من سلام 🌟

از امروز هر وقت خواستی محصولی رو خرید کنی  می‌تونی خیلی سریع کمترین قیمت محصول رو پیدا کنی  و همینطور ببینی کدوم فروشگاه‌های رسمی این محصول رو دارن  و اگه خواستی از اونجا محصول مورد نظرت رو تأمین کنی که مطمئن باشی ✅

برای این کار کافیه از محصول مورد نظرت عکس  یا اسکرین شات بگیری  و همینجا بفرستی یا لینک محصول رو با دایرکت همین پیج شیر کنی 📲

ما در لحظه محصول مشابه با پایین‌ترین قیمت‌ها رو واست ارسال می‌کنیم 📦🎯";


		$this->messageService->sendTextMessage($senderId, $text);
	}

	public function addImageMessage(string $instagramId, string $imageUrl): int
	{
		return $this->userRepository->addImageMessage($instagramId, $imageUrl);
	}

	public function addTextMessage(string $instagramId, string $query): int
	{
		return $this->userRepository->addTextMessage($instagramId, $query);
	}

	public function updateMessage(int $messageId, array $attributes): void
	{
		$this->messageRepository->update($messageId, $attributes);
	}
}
