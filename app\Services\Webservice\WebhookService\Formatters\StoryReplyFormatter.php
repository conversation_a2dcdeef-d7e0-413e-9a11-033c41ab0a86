<?php

namespace App\Services\Webservice\WebhookService\Formatters;

class StoryReplyFormatter
{
    public function format(array $message): array
    {
        return [
            'sender'       => $message['sender']['id'],
            'receiver'     => $message['recipient']['id'],
            'message_type' => 'story_reply',
            'reply_text'   => $message['message']['text'],
            'story_url'    => $message['message']['reply_to']['story']['url'],
            'story_id'     => $message['message']['reply_to']['story']['id'],
            'timestamp' => gmdate('Y-m-d\TH:i:s\Z', intdiv((int)$message['timestamp'], 1000)),
            'is_admin'     => isset($message['message']['is_echo']),
        ];
    }
}
