<?php
/**
 * chatbot/messenger.php
 *
 * This script handles incoming POST requests with specific parameters, `url` and `payload`,
 * and forwards the payload to the specified `url` which is an instagram url, using a cURL POST request.
 * It captures and returns the response from the target URL.
 *
 * ## Expected Request Format:
 * - **Method**: POST
 * - **Content-Type**: application/x-www-form-urlencoded
 * - **Parameters**:
 *   - `url`: The target URL to which the `payload` data should be sent.
 *   - `payload`: JSON-encoded data to be sent as the POST body.
 *
 * ## Response Format:
 * - **Content-Type**: application/json
 * - **Fields**:
 *   - `status`: (string) "success" or "error" indicating the result of the request.
 *   - `message`: (string) A message describing the result (e.g., "Request successful" or an error message).
 *   - `data`: (string|null) The response body from the target URL, if available.
 *
 * ## Response Codes:
 * - `200 OK`: The request was successful, and the data field contains the response from the target URL.
 * - `400 Bad Request`: Either the `url` or `payload` parameter was missing.
 * - `500 Internal Server Error`: A cURL error occurred, and the error message is returned.
 * - Other HTTP codes may also be returned if the target URL responds with an error.
 */


header('Content-Type: application/json');

$responseData = [
    'status' => 'error',
    'message' => '',
    'data' => null,
];


if (!isset($_POST['url']) || !isset($_POST['payload'])) {
    http_response_code(400);
    $responseData['message'] = 'Missing required parameters';
    echo json_encode($responseData);
    exit();
}

$url = $_POST['url'];
$payload = $_POST['payload'];

$ch = curl_init($url);
curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);

if ($response === false) {
    $responseData['status'] = 'error';
    $responseData['message'] = 'cURL error: ' . curl_error($ch);
    http_response_code(500);

    curl_close($ch);
    echo json_encode($responseData);
    exit();
}

$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode >= 200 && $httpCode < 300) {
    http_response_code(200);
    $responseData['status'] = 'success';
    $responseData['message'] = 'Request successful';
    $responseData['data'] = $response;
} else {
    http_response_code($httpCode);
    $responseData['message'] = 'Request failed';
    $responseData['data'] = $response;
}

echo json_encode($responseData);
exit();
