<?php

namespace App\Services\Webservice\MessageService;

use App\Services\Webservice\Abstract\AbstractMessageService;

class VideoMessageService extends AbstractMessageService
{
    protected string $attachmentType = 'video';

    /**
     * Gets the message content for a video message.
     *
     * @param array $messageData
     * @return array
     */
    protected function getMessageContent(array $messageData): array
    {
        return [
            'attachment' => [
                'type' => $this->attachmentType,
                'payload' => [
                    'url' => $messageData['video_url'],
                ],
            ],
        ];
    }
}
