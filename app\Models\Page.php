<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Page extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id',
        'title',
        'access_token',
        'page_user_id',
        'fb_user_id',
        'fb_access_token',
        'profile'
    ];
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
    public function contents(): HasMany
    {
        return $this->hasMany(PageContent::class);
    }
    public function paymentMethods(): HasMany
    {
        return $this->hasMany(PaymentMethod::class);
    }
    // Last successful transaction for the page
    // The function was named "transaction" to shorten the object keys of json responses
    public function transaction(): HasOne
    {
        return $this->hasOne(Transaction::class)
            ->where('status', 'approved')
            ->orderByDesc('created_at');
    }

    public function getFbAccessTokenAttribute($encryptedAt)
    {
        $at = null;
        try {
            $encrypted = json_decode($encryptedAt);
            $at = decryptData(
                base64_decode($encrypted[0]),
                base64_decode($encrypted[1]),
                base64_decode($encrypted[2])
            );
        } catch (\Throwable $th) {
            $at = $encryptedAt;
        }

        return $at;
    }
    public function getAccessTokenAttribute($encryptedAt)
    {
        $at = null;
        try {
            $encrypted = json_decode($encryptedAt);
            $at = decryptData(
                base64_decode($encrypted[0]),
                base64_decode($encrypted[1]),
                base64_decode($encrypted[2])
            );
        } catch (\Throwable $th) {
            $at = $encryptedAt;
        }

        return $at;
    }
}
