<?php

namespace App\Services\SmsService;

use App\Contracts\SMS\SmsInterface;
use Exception;
use Illuminate\Support\Facades\Http;

class SapakService implements SmsInterface
{
    private string $apiKey;
    private string $baseUrl;
    private string $from;

    public function __construct()
    {
        $this->apiKey  = env('SAPAK_API_KEY');
        $this->baseUrl = env('SAPAK_BASE_URL');
        $this->from    = env('SAPAK_FROM');
    }

    /**
     * Sends a generic SMS message with Sapak Service.
     *
     * @throws Exception
     */
    public function sendSms(string $to, string $message): array
    {
        $payload = [
            'from' => $this->from,
            'to' => [$to],
            'text' => $message,
        ];

        try {
            $response = Http::withHeaders([
                '-X-API-KEY' => $this->apiKey,
            ])->post("{$this->baseUrl}/v1/messages", $payload);

            if ($response->failed()) {
                throw new Exception("Failed to send SMS: {$response->body()}");
            }

            return $response->json();
        } catch (Exception $exception) {
            throw new Exception("SMS sending error: " . $exception->getMessage());
        }
    }

    public function sendTransactionSuccessSms(
        string $to,
        string $pageUsername,
        string $time,
        string $date,
        string $trackingCode,
        string $amount
    ): array
    {
        $message = "{$pageUsername} کاربر عزیز
                درساعت {$time} به تاریخ {$date} یک واریزی با شماره پیگیری {$trackingCode} به مبلغ {$amount} به حساب شما ثبت گردید.
                ";

        return $this->sendSms($to, $message);
    }
}
