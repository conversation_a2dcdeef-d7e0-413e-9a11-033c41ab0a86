<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class OrderAttribute extends Model
{
    use HasFactory,SoftDeletes;
    protected $fillable = [
        'value',
        'order_id',
        'product_id',
        'product_attributes_id'
    ];
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
    public function order(): BelongsTo
    {
        return $this->belongsTo(order::class);
    }
    public function product(): BelongsTo
    {
        return $this->belongsTo(Products::class);
    }
    public function productAttributes(): BelongsTo
    {
        return $this->belongsTo(productAttributes::class);
    }
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }
}
