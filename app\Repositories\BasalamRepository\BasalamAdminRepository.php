<?php

namespace App\Repositories\BasalamRepository;

use App\Models\Page;

class BasalamAdminRepository
{
    private string $adminId = "17841470899169358";


    public function getAdminToken(): string
    {
        $adminPage = Page::Where('page_user_id', $this->adminId)->first();
        return $adminPage->access_token;
    }
    public function setAdminId()
    {
        //
    }

    public function getAdminId(): string
    {
        return $this->adminId;
    }


}
