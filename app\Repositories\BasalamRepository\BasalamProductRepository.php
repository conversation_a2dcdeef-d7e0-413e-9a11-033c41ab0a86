<?php

namespace App\Repositories\BasalamRepository;

use App\Models\Basalam\BasalamProduct as Product;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;

/**
 * BasalamProductRepository class
 *
 * This repository acts as a layer between the application and the underlying
 * database, providing methods to manage Basalam products by interacting with the
 * BasalamProduct model.
 *
 * Properties:
 * - $productToSaveNumber: Defines the number of products to be saved at a time.
 * - $productUniqueTime: Stores a unique time used as a key for identifying specific products.
 *
 * Methods:
 * - saveProducts(array $products): Processes and saves products to the database, and returns the
 *   IDs of recently created products along with a unique timestamp.
 * - prepareProductData(array $product): Prepares product data for saving to the database.
 * - cleanDescription(string $description): Cleans and formats the product description.
 * - setProductUniqueTime(): Sets a unique timestamp for the products.
 * - sendCreatedProductsDetails(array $filteredProducts): Returns the IDs of recently created products
 *   and their unique timestamp.
 * - getSavedProductIds(array $products): Retrieves the IDs of the recently created products.
 * - getProduct(int $product_id): Retrieves a Basalam product by its product ID.
 * - getProductsByIdsAndTime(array $productIds, $productTime): Retrieves Basalam products by their IDs
 * and unique timestamp, ensuring the timestamp is properly formatted.
 */
class BasalamProductRepository
{

    /**
     * The number of products to be stored.
     *
     * @var int
     */
    protected int $productToSaveNumber = 10;

    /**
     * The unique time to be used as a unique key for finding a specific product.
     *
     * @var Carbon
     */
    protected Carbon $productUniqueTime;


    /**
     * Processes and saves products to the database.
     *
     * Receives products from which has been fetched from Basalam API and stores some
     * of them in the database.
     *
     * After storing Basalam products, returns an array, including the ids of
     * recently created products along with a unique time  as a unique key.
     *
     * @param array $products Products coming from Basalam
     * @return array The list of product Ids that just created.
     */
    public function saveProducts(array $products): array
    {
        $this->setProductUniqueTime();
        $filteredProducts = array_slice($products, 0, $this->productToSaveNumber);

        foreach ($filteredProducts as $product) {
            $productData = $this->prepareProductData($product);

            Product::create($productData);
        }

        return $this->sendCreatedProductsDetails($filteredProducts);
    }

    /**
     * Prepares the product data for saving.
     *
     * @param array $product
     * @return array
     */
    protected function prepareProductData(array $product): array
    {
	    return [
		    'product_id'          => $product['id'],
		    'product_unique_time' => $this->productUniqueTime,
		    'name'                => $product['name'] ?? 'Unknown Product',
		    'description'         => $this->cleanDescription($product['description'] ?? ''),
		    'price'               => $product['price'] / 10 ?? null,
		    'photo_medium'        => $product['photo']['MEDIUM'] ?? null,
		    'average_rating'      => $product['rating']['average'] ?? null,
		    'rating_count'        => $product['rating']['count'] ?? 0,
		    'status'              => $product['status']['title'] ?? null,
		    'owner_name'          => $product['vendor']['owner']['name'] ?? '',
		    'owner_city'          => $product['vendor']['owner']['city'] ?? '',
		    'owner_id'            => $product['vendor']['owner']['id'] ?? -1,
		    'vendor_identifier'   => $product['vendor']['identifier'] ?? null,
		    'has_delivery'        => $product['vendor']['has_delivery'] ?? false,
		    'is_available'        => $product['IsAvailable'] ?? false,
		    'can_add_to_cart'     => $product['canAddToCart'] ?? false,
		    'preparation_days'    => $product['preparationDays'] ?? 0,
		    'stock'               => $product['stock'] ?? 0,
		    'weight'              => $product['weight'] ?? 0,
	    ];
    }

    /**
     * Cleans the product description.
     *
     * @param string $description
     * @return string
     */
    protected function cleanDescription(string $description): string
    {
        $description = str_replace("\t", ' ', $description);
        $description = str_replace("\n", ' ', $description);

        return preg_replace('/\s+/', ' ', $description);
    }

    /**
     * Sets a unique time for products.
     *
     * Since products are unique with their product_id and product_unique_time
     * we set the timestamp of now as the unique time.
     *
     * @return void
     */
    protected function setProductUniqueTime(): void
    {
        $this->productUniqueTime = Carbon::now()->utc();
    }

    /**
     * Returns the recent created Basalam Products along with the unique time.
     *
     * @param $filteredProducts
     * @return array
     */
    protected function sendCreatedProductsDetails($filteredProducts): array
    {
        return [
            'product_ids' => $this->getSavedProductIds($filteredProducts),
            'timestamp'   => $this->productUniqueTime,
        ];
    }

    /**
     * Gets the Ids of recent created Basalam Products.
     *
     * @param $products
     * @return array
     */
    protected function getSavedProductIds($products): array
    {
        $productIds = [];

        foreach ($products as $product) {
            $productIds[] = $product['id'];
        }

        return $productIds;
    }

    /**
     * Returns An instance of Basalam Product based on a product_id.
     *
     * @param int $product_id
     * @return Product A Basalam Product instance
     */
    public function getProduct(int $product_id): Product
    {
        return Product::where('product_id', $product_id)->first();
    }

    /**
     * Retrieves products by their IDs and unique timestamp.
     *
     * This method fetches Basalam Products based on an array of product IDs
     * and a specific product unique timestamp.
     *
     * @param array $productIds Array of product IDs to filter.
     * @param string|Carbon $productTime The unique timestamp of the products.
     * @return Collection The collection of matched products.
     */
    public function getProductsByIdsAndTime(array $productIds, Carbon|string $productTime): Collection
    {
        $formattedTime = Carbon::parse($productTime)->format('Y-m-d H:i:s');

        return Product::whereIn('product_id', $productIds)
            ->where('product_unique_time', $formattedTime)
            ->get();
    }

    /**
     * Deletes Basalam products older than the specified age.
     *
     * @param int $days The number of days after which products are considered expired.
     * @return int The number of deleted products.
     */
    public static function deleteExpiredProducts(int $days): int
    {
        $cutoffDate = Carbon::now()->utc()->subDays($days);

        return Product::where('created_at', '<', $cutoffDate)->delete();
    }
}
