<?php

namespace App\Services\BasalamService;

use App\Services\BasalamService\Abstract\AbstractWebhookValidator;

class ImageValidator extends AbstractWebhookValidator
{
    public function validate(array $data): ?string
    {
        if (isset($data['object']) && $data['object'] === 'instagram') {
            if (isset($data['entry']) && is_array($data['entry'])) {
                foreach ($data['entry'] as $entry) {
                    if (isset($entry['messaging']) && is_array($entry['messaging'])) {
                        foreach ($entry['messaging'] as $messageData) {
                            if (isset($messageData['message']['attachments']) && is_array($messageData['message']['attachments'])) {
                                foreach ($messageData['message']['attachments'] as $attachment) {
                                    if (isset($attachment['payload']['url'])) {
                                        return $attachment['payload']['url'];
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return null;
    }
}
