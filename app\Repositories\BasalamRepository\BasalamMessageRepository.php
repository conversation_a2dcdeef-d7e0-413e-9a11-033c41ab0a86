<?php

namespace App\Repositories\BasalamRepository;

use App\Models\Basalam\BasalamMessage;

class BasalamMessageRepository
{
	/**
	 * Creates a new BasalamMessage instance.
	 *
	 * @param array $attributes The message attributes.
	 * @return BasalamMessage The created message instance.
	 */
	public function create(array $attributes): BasalamMessage
	{
		return BasalamMessage::query()->create($attributes);
	}

	/**
	 * Updates a BasalamMessage instance.
	 *
	 * @param int $id The message ID.
	 * @param array $attributes The message attributes.
	 * @return BasalamMessage The updated message instance.
	 */
	public function update(int $id, array $attributes): BasalamMessage
	{
		$message = BasalamMessage::query()->findOrFail($id);
		$message->update($attributes);

		return $message;
	}
}
