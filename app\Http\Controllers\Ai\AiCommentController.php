<?php

namespace App\Http\Controllers\Ai;

use App\Http\Controllers\Controller;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

/**
 * Class AiCommentController
 *
 * This controller acts as a **pass-through (proxy)** for forwarding incoming webhook requests
 * to the external `shop_ai` microservice, which handles AI-related operations such as comment generation,
 * product analysis, or content moderation.
 *
 * ### Purpose:
 * - This controller does not process or validate the webhook request itself.
 * - It simply forwards the request payload to the configured `shop_ai` microservice endpoint.
 * - Adds an API Key for secure communication with the external service.
 */
class AiCommentController extends Controller
{
    /**
     * The full URL to the webhook endpoint of the Shop AI microservice.
     *
     * @var string
     */
    protected string $apiUrl;

    /**
     * The API key used to authenticate with the Shop AI microservice.
     *
     * @var string
     */
    protected string $apiKey;

    /**
     * AiCommentController Constructor.
     *
     * Retrieves the base URL and API key for the Shop AI microservice
     * from the application's configuration (typically stored in `config/services.php`).
     * It constructs the complete webhook endpoint for forwarding requests.
     */
    public function __construct()
    {
        $this->apiUrl = config('services.shop_ai.base_url') . '/webhook/instagram';
        $this->apiKey = config('services.shop_ai.api_key');
    }

    /**
     * Handles incoming webhook requests by forwarding them to the Shop AI microservice.
     *
     * This method sends a POST request to the remote AI service with the incoming
     * request's payload and includes the configured API key in the headers.
     *
     * @param Request $request The incoming HTTP request from the webhook
     * @return Response The response returned by the Shop AI microservice
     * @throws ConnectionException If the request to the AI service fails (e.g., service is unreachable)
     */
    public function handle(Request $request): Response
    {
        return Http::withHeaders(['X-API-KEY' => $this->apiKey])
            ->post($this->apiUrl, $request->all());
    }
}
