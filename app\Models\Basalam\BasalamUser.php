<?php

namespace App\Models\Basalam;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class BasalamUser extends Model
{
    use HasFactory;

    protected $table = 'basalam_users';

	protected $fillable = [
		'instagram_id',
		'is_follower',
		'message_count',
	];

	protected $casts = [
		'is_follower' => 'boolean',
	];

    /**
     * Check if a user with a given Instagram ID exists.
     *
     * @param  string  $instagramId
     * @return bool
     */
    public static function hasInstagramId($instagramId): bool
    {
        return self::where('instagram_id', $instagramId)->exists();
    }

    public function reviews(): HasMany
    {
        return $this->hasMany(BasalamReview::class);
    }

    public function messages(): HasMany
    {
	    return $this->hasMany(BasalamMessage::class, 'user_id');
    }
}
