<?php

namespace App\Services\Webservice\Validators;

use App\Exceptions\Webservice\InvalidEndpointException;
use App\Models\Webservice\SpecialUser;

class ValidEndpointValidationService
{
    /**
     * Validates if the request is coming from a valid endpoint.
     *
     * @param SpecialUser $specialUser
     * @param string $requestOrigin
     * @return void
     * @throws InvalidEndpointException
     */
    public static function validateEndpoint(SpecialUser $specialUser, string $requestOrigin): void
    {
        if (!$specialUser->valid_endpoints) {
            throw new InvalidEndpointException(
                requestOrigin: $requestOrigin, message: "Unfortunately You defined to valid endpoint yet"
            );
        }

        $validEndpoints = json_decode($specialUser->valid_endpoints, true);
        if (!self::isValidEndpoint($validEndpoints, $requestOrigin)) {
            throw new InvalidEndpointException(requestOrigin: $requestOrigin);
        }
    }

    /**
     * Returns true if an endpoint/ip is valid
     *
     * @param array $validEndpoints
     * @param string $requestOrigin
     * @return bool
     */
    private static function isValidEndpoint(array $validEndpoints, string $requestOrigin): bool
    {
        return in_array($requestOrigin, $validEndpoints);
    }
}
