<?php

namespace App\Services\Webservice\WebhookService\Formatters;

class MediaMessageFormatter
{
    public function format(array $message, string $type): array
    {
        return [
            'sender'       => $message['sender']['id'],
            'receiver'     => $message['recipient']['id'],
            'message_type' => 'media',
            'media' => [
                'url'  => $message['message']['attachments'][0]['payload']['url'],
                'type' => $type,
            ],
            'timestamp' => gmdate('Y-m-d\TH:i:s\Z', intdiv((int)$message['timestamp'], 1000)),
            'is_admin'  => isset($message['message']['is_echo']),
        ];
    }
}
