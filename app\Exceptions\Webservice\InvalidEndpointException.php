<?php

namespace App\Exceptions\Webservice;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class InvalidEndpointException extends Exception
{
    protected string $requestOrigin;

    /**
     * Creates a new exception instance.
     *
     * @param string $requestOrigin
     * @param string|null $message
     */
    public function __construct(string $requestOrigin, $message = null)
    {
        $this->requestOrigin = $requestOrigin;

        $message = $message ?: "Request from invalid endpoint: '{$requestOrigin}'";

        parent::__construct($message);
    }

    /**
     * Gets the request origin (the domain or server).
     *
     * @return string
     */
    public function getRequestOrigin(): string
    {
        return $this->requestOrigin;
    }

    /**
     * Reports the exception to the logs or external services.
     *
     * @return void
     */
    public function report(): void
    {
        Log::error("Invalid endpoint: {$this->requestOrigin}", ['exception' => $this->getMessage()]);
    }

    /**
     * Renders the exception into an HTTP response.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function render(Request $request): JsonResponse
    {
        return response()->json([
            'error'          => $this->getMessage(),
            'request_origin' => $this->getRequestOrigin(),
        ], status: 403);
    }
}
