<?php

namespace App\Http\Controllers\Webhook\InstagramWebhook;

use App\Contracts\SMS\SmsInterface;
use App\Http\Controllers\Ai\AiCommentController;
use App\Http\Controllers\Basalam\MainController as BasalamController;
use App\Http\Controllers\Idea\MainController as IdeaController;
use App\Http\Controllers\Webservice\WebhookController as WebserviceController;
use App\Http\Controllers\Controller;
use App\Models\Webservice\SpecialUser;
use App\Services\AiService\AiSettingService;
use App\Services\WebhookServices\DeleteWebhookStates;
use App\Models\BillTrigger;
use App\Models\Customer;
use App\Models\ManymessageAgent;
use App\Models\order;
use App\Models\OrderAttribute;
use App\Models\Page;
use App\Models\ProductPrice;
use App\Models\Product;
use App\Models\Settings;
use App\Models\User as UserModel;
use App\Services\PaymentGatewayService;
use App\Services\PersianParserService\PersianParserService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;

class InstagramWebhookController extends Controller
{
    protected PersianParserService $persianParser;
    protected SmsInterface $smsService;
    protected AiSettingService $aiSettingService;

    public function __construct(
        PersianParserService $persianParser,
        SmsInterface $smsInterface,
        AiSettingService $aiSettingService,
    ) {
        if ($_SERVER['REQUEST_METHOD'] == 'GET') {
            if (isset($_GET['hub_mode']) && isset($_GET['hub_verify_token']) && $_GET['hub_mode'] === 'subscribe') {
                echo $_GET['hub_challenge'];
                exit;
            }
        }

        $this->persianParser = $persianParser;
        $this->smsService = $smsInterface;
        $this->aiSettingService = $aiSettingService;
    }

    public function handle(Request $request)
    {

        if ($this->isIdeaPage($request)) {
            $this->sendToIdeaController($request);
            exit();
        }

        if (!$this->isInstagramWebhook($request)) {
            exit();
        }



        if ($this->mustIgnoreWebhook($request)) {
            $dataEncode = json_encode($request->all());
            $this->sendToTelegram2(text: "Webhook: " . $dataEncode, chat_id: 5883683450);
            exit();
        }

        if ($this->isManyMessageUser($request)) {
            $this->sendToManyMessageService($request);
            return;
        }

        //        if ($this->isBasalamPage($request)) {
        //            $this->sendToBasalamController($request);
        //            exit();
        //        }

        if ($this->isSpecialUser($request)) {
            $this->sendToWebserviceController($request);
            exit();
        }

        //        if ($this->isCommentAiPost($request)) {
        //            $dataEncode = json_encode($request->all());
        //            $this->sendToTelegram2(text: "Request to AI Service: " . $dataEncode, chat_id: 5883683450);
        //            $this->sendToAiCommentsController($request);
        //            exit();
        //        }

        return response()->json(["success" => true, 'message' => 'Webhook Processed.']);

        //$this->sendToTelegram2(text: "handle", chat_id: 5883683450);
        // if($request->ip()!=='************'){
        //     return;
        // }

        // $content = file_get_contents("php://input");
        // $json = json_decode($content,true);
        // if ($this->isVerificationRequest($request)) {
        //     echo $request->hub_challenge;
        // }
    }
    private function isVerificationRequest($request)
    {
        //        $this->sendToTelegram2(text: "testing VerificationRequest", chat_id: 5883683450);
        if (!$request->isMethod('get')) {
            return false;
        }
        if ($request->has('hub_mode') && $request->has('hub_verify_token')) {
            // \Log::info($request->hub_mode === 'subscribe');
            return $request->hub_mode === 'subscribe';
        }
        return false;
    }


    private  function sendToTelegram2($text, $chat_id)
    {
        return;
        $botApiToken = '7209528727:AAHdPocptgt1aBaLDDXovIGZoRsOEPFrHoU';
        $channelId = $chat_id;

        $query = http_build_query([
            'chat_id' => $channelId,
            'text' => $text,
        ]);
        $url = "https://api.telegram.org/bot{$botApiToken}/sendMessage?{$query}";

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => 'GET',
        ));
        curl_exec($curl);
        curl_close($curl);
    }

    private function sendMessageToUser($access_token, $id, $msg, $cancel = false, $back = false)
    {
        $this->sendToTelegram2(text: "51", chat_id: 5883683450);
        if ($cancel) {
            $payload = [
                'recipient' => ['id' => $id],
                'messaging_type' => 'RESPONSE',
                'message' => [
                    'text' => $msg,
                    'quick_replies' => [
                        [
                            'content_type' => 'text',
                            'title' => 'کنسل',
                            'payload' => 'CancelOrder'
                        ]
                    ]
                ]
            ];

            $this->sendToTelegram2(text: "51: step 1", chat_id: 5883683450);

            if ($back) {
                $this->sendToTelegram2(text: "51: step 1 x", chat_id: 5883683450);
                $payload['message']['quick_replies'][] = [
                    'content_type' => 'text',
                    'title' => 'قبلی',
                    'payload' => 'BackOrder'
                ];
            }
        } else {
            $this->sendToTelegram2(text: "51: step 1 y", chat_id: 5883683450);
            $payload = ['recipient' => ['id' => $id], 'message' => ['text' => $msg]];
        }
        $this->sendToTelegram2(text: "51: step 2", chat_id: 5883683450);
        $facebookUrl = 'https://graph.instagram.com/v20.0/me/messages?access_token=' . $access_token;
        $payload = [
            'url' => $facebookUrl,
            'payload' => json_encode($payload)
        ];
        $this->sendToTelegram2(text: "51: step 2", chat_id: 5883683450);
        $response = $this->forwardRequest($payload);
        $this->sendToTelegram2(text: "51: step 3", chat_id: 5883683450);
        $this->sendToTelegram2(text: "51: response: " . json_encode($response), chat_id: 5883683450);
        return $response;
    }

    private function forwardRequest(array $payload): JsonResponse
    {
        try {
            $response = Http::timeout(10)
                ->withHeaders(['Content-Type' => 'application/json'])
                ->post($payload['url'], json_decode($payload['payload'], true));

            return response()->json([
                'status' => $response->successful() ? 'success' : 'error',
                'message' => $response->successful() ? 'Request successful' : 'Request failed',
                'data' => $response->body(),
            ], $response->status());
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Request failed: ' . $e->getMessage(),
                'data' => null,
            ], 500);
        }
    }

    private function isInstagramWebhook($request): bool
    {
        $dataEncode = json_encode($request->all());

        if (isset($request['entry']) && is_array($request['entry'])) {
            foreach ($request['entry'] as $entry) {
                if (isset($entry['id'])) {
                    $pageUserId = $entry['id'];
                    if ($pageUserId == '17841470170944577' | $pageUserId == '17841470089607057') {
                        $this->sendToTelegram1(text: "The webhook: " . $dataEncode, chat_id: 5883683450);
                    }
                    //                    $this->sendToTelegram2(text: "id isInstagram: " . $pageUserId, chat_id: 5883683450);

                    $page = Page::where("page_user_id", $pageUserId)->first();

                    if ($page && $page->fb_user_id === "loggedInWithInstagram") {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    private  function sendToTelegram1($text, $chat_id)
    {

        $botApiToken = '7892323657:AAGOrMFIBAQU8POIe0Z4gG8NmbjPmZnJJM4';
        $channelId = $chat_id;

        $query = http_build_query([
            'chat_id' => $channelId,
            'text' => $text,
        ]);
        $url = "https://api.telegram.org/bot{$botApiToken}/sendMessage?{$query}";

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => 'GET',
        ));
        curl_exec($curl);
        curl_close($curl);
    }

    private function mustIgnoreWebhook($request): bool
    {
        $data = $request->all();

        foreach ($data['entry'] as $entry) {
            if ($entry['id'] == "17841457220240673") {
                return true;
            }
        }

        return false;
    }

    private function isBasalamPage(Request $request): bool
    {
        $data = $request->all();

        foreach ($data['entry'] as $entry) {
            if ($entry['id'] == "17841470899169358") {
                return true;
            }
        }

        return false;
    }

    private function isIdeaPage(Request $request): bool
    {
        $data = $request->all();

        foreach ($data['entry'] as $entry) {
            if ($entry['id'] == "17841462981311951") {
                return true;
            }
        }

        return false;
    }

    private function isSpecialUser(Request $request): bool
    {
        $data = $request->all();

        foreach ($data['entry'] as $entry) {
            if (SpecialUser::hasInstagramId($entry['id'])) {
                return true;
            }
        }

        return false;
    }

    private function isCommentAiPost(Request $request): bool
    {
        $data = $request->all();

        if (isset($data['entry'][0]['changes'][0]['value']['media']['id'])) {

            $mediaId = $data['entry'][0]['changes'][0]['value']['media']['id'];
            if ($mediaId) {
                if ($this->aiSettingService->hasAiSetting(postId: $mediaId)) {
                    return true;
                }
            }
        }

        return false;
    }

    private function sendToBasalamController(Request $request): void
    {
        $basalamController = app()->make(BasalamController::class);
        $basalamController->handle($request);
    }

    private function sendToIdeaController(Request $request): void
    {
        $ideaController = app()->make(IdeaController::class);
        $ideaController->handle($request);
    }

    private function sendToWebserviceController(Request $request): void
    {
        $webserviceController = app()->make(WebserviceController::class);
        $webserviceController->handle($request);
    }

    private function sendToAiCommentsController(Request $request): void
    {
        $aiCommentsController = app()->make(AiCommentController::class);
        $aiCommentsController->handle($request);
    }

    /**
     * Sends webhook data to many message service
     * @param \Illuminate\Http\Request $request
     * @return void
     */
    private function sendToManyMessageService(Request $request): void
    {
        try {
            $response = Http::timeout(10)
                ->withHeaders(['Content-Type' => 'application/json'])
                ->post('https://panel.manymessage.com/bot/reader.php', $request->all());

            // Log the response for debugging if needed
            if (!$response->successful()) {
                \Log::warning('Failed to send data to many message service', [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('Error sending data to many message service: ' . $e->getMessage());
        }
    }

    /**
     * Checks if special user has email .
     * @param \Illuminate\Http\Request $request
     * @return void
     */
    private function isManyMessageUser(Request $request): bool
    {
        $data = $request->all();

        foreach ($data['entry'] as $entry) {
            if (ManymessageAgent::instaId($entry['id'])->exists()) {
                return true;
            }
        }

        return false;
    }
}
