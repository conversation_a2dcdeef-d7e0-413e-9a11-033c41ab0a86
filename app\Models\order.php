<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Laravel\Scout\Searchable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;
class order extends Model
{
    use HasFactory,Searchable,SoftDeletes;
    // public function searchableAs()
    // {
    //     return 'orders_index';
    // }
    public function getScoutKey(): mixed
    {
        return $this->id;
    }
    public function getScoutKeyName(): mixed
    {
        return 'id';
    }

    protected function makeAllSearchableUsing(Builder $query): Builder
    {
        return $query->with(['customer','product']);
    }
    public function toSearchableArray(): array
    {
        // $array = $this->toArray();
        // return $array;
        $this->load(['customer','product']);
        \Log::info($this->customer);
        if($this->customer===null&&$this->product===null){
            return [
                'id'=> $this->getKey(),
                'ref_num' => $this->ref_num,
                'payment_amount' => $this->payment_amount,
                'token'=> $this->token,
                'card_number'=> $this->card_number,
                'status'=>$this->status,
                'tracking_code'=> $this->tracking_code,
                'tag'=> $this->tag,
                'user_id'=>(int) $this->user_id,
                'customer_id'=>(int) $this->customer_id,
                'product_id'=>(int) $this->product_id,
                'created_at'=>$this->created_at->timestamp
            ];
        }
        if($this->customer===null){
            return [
                'id'=> $this->getKey(),
                'ref_num' => $this->ref_num,
                'payment_amount' => $this->payment_amount,
                'token'=> $this->token,
                'card_number'=> $this->card_number,
                'status'=>$this->status,
                'tracking_code'=> $this->tracking_code,
                'tag'=> $this->tag,
                'user_id'=>(int) $this->user_id,
                'customer_id'=>(int) $this->customer_id,
                'product_id'=>(int) $this->product_id,
                'product_name'=>$this->product->name??"",
                'created_at'=>$this->created_at->timestamp
            ];
        }
        if($this->product===null){
            return [
                'id'=> $this->getKey(),
                'ref_num' => $this->ref_num,
                'payment_amount' => $this->payment_amount,
                'token'=> $this->token,
                'card_number'=> $this->card_number,
                'status'=>$this->status,
                'tracking_code'=> $this->tracking_code,
                'tag'=> $this->tag,
                'user_id'=>(int) $this->user_id,
                'customer_id'=>(int) $this->customer_id,
                'customer_name'=>$this->customer->name??"",
                'product_id'=>(int) $this->product_id,
                'created_at'=>$this->created_at->timestamp
            ];
        }
        return [
            'id'=> $this->getKey(),
            'ref_num' => $this->ref_num,
            'payment_amount' => $this->payment_amount,
            'token'=> $this->token,
            'card_number'=> $this->card_number,
            'status'=>$this->status,
            'tracking_code'=> $this->tracking_code,
            'tag'=> $this->tag,
            'user_id'=>(int) $this->user_id,
            'customer_id'=>(int) $this->customer_id,
            'customer_name'=>$this->customer->name??"",
            'product_id'=>(int) $this->product_id,
            'product_name'=>$this->product->name??"",
            'created_at'=>$this->created_at->timestamp
        ];

    }
    public function orderAttributes(): HasMany
    {
        return $this->hasMany(OrderAttribute::class);
    }
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_id','id');
    }
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class,'product_id','id')->withTrashed();
    }
    public function productPrices(): BelongsTo
    {
        return $this->belongsTo(ProductPrice::class, 'product_prices_id');
    }
    public function deliveryMethod(): BelongsTo
    {
        return $this->belongsTo(DeliveryMethod::class, 'delivery_method_id');
    }
    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class, 'payment_method_id');
    }
}
