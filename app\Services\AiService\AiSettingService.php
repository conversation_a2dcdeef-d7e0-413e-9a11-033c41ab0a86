<?php

namespace App\Services\AiService;

use App\Repositories\Interfaces\AiSettingRepositoryInterface as AiSettingRepoInterface;

/**
 * Class AiSettingService
 *
 * Service class responsible for managing AI settings via interacting with the repository
 * to perform certain operations on the `AiSetting` model.
 */
class AiSettingService
{
    /**
     * The repository instance for interacting with AI settings.
     *
     * @var AiSettingRepoInterface
     */
    private AiSettingRepoInterface $repository;

    public function __construct(AiSettingRepoInterface $aiSettingRepository)
    {
        $this->repository = $aiSettingRepository;
    }

    /**
     * Checks if AI settings exist for the given post ID.
     *
     * This method queries the repository to check if an AI setting with the given
     * `post_id` already exists in the database.
     *
     * @param string $postId The post ID to check for the existence of an AI setting
     * @return bool `true` if the AI setting exists for the given post ID, `false` otherwise
     */
    public function hasAiSetting(string $postId): bool
    {
        return $this->repository->existsForPostId($postId);
    }
}
