<?php

namespace App\Services\Webservice\MessageService;

use App\Services\Webservice\Abstract\AbstractMessageService;

class ButtonTextMessageService extends AbstractMessageService
{
	protected string $attachmentType = 'template';

	/**
	 * Gets the message content for a text message.
	 *
	 * @param array $messageData
	 * @return array
	 */
	protected function getMessageContent(array $messageData): array
	{
		return [
			'attachment' => [
				'type'    => $this->attachmentType,
				'payload' => [
					'template_type' => 'button',
					'text'          => $messageData['text'],
					'buttons'       => $messageData['buttons'],
				],
			],
		];
	}
}
