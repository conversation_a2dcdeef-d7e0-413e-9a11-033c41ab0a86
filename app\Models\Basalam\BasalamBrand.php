<?php

namespace App\Models\Basalam;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class BasalamBrand extends Model
{
	protected $table = 'basalam_brands';
	public $timestamps = false;

	protected $fillable = [
		'category_id',
		'title',
	];

	public function category(): BelongsTo
	{
		return $this->belongsTo(BasalamCategory::class);
	}

	public function messages(): HasMany
	{
		return $this->hasMany(BasalamMessage::class);
	}
}
