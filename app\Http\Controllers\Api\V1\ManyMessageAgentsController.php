<?php

namespace App\Http\Controllers\Api\V1;

use App\DTOs\UpdateOrCreateManyMessageAgentDTO;
use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\ManyMessageAgents\UpdateOrCreateManyMessageAgentRequest;
use App\Http\Resources\ManyMessageAgentResource;
use App\Services\Actions\ManyMessageAgents\UpdateOrCreateManyMessageAgent;

class ManyMessageAgentsController extends BaseController
{
    public function store(UpdateOrCreateManyMessageAgentRequest $request, UpdateOrCreateManyMessageAgent $action)
    {
        $dto =  UpdateOrCreateManyMessageAgentDTO::fromRequest($request);
        $agent = $action->handle($dto);

        return $this->sendResponse(new ManyMessageAgentResource($agent));
    }
}
