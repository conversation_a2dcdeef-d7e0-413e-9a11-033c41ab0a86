<?php

namespace App\Services\GatewayService;

use App\Services\SnappPayService\Enums\Currency;
use App\Services\SnappPayService\Order\Order;
use App\Services\SnappPayService\Order\OrderProduct;
use App\Services\SnappPayService\Order\ProductCategory;
use App\Services\SnappPayService\Core\SnappPay;
use App\Services\SnappPayService\Core\SnappPaySetting;
use App\Contracts\Gateway\PaymentGateway;
use Illuminate\Support\Facades\Http;

class SnappGateway implements PaymentGateway
{
    private $setting;
    public function __construct(
        string $user,
        string $pass,
        string $clientId,
        string $secret,
    ) {
        if($user==='directam-purchase'){
            $snappPaySetting = SnappPaySetting::credentials(
                $user,
                $pass,
                $clientId,
                $secret,
                'https://fms-gateway-staging.apps.public.okd4.teh-1.snappcloud.io'
            );
        }else{
            $snappPaySetting = SnappPaySetting::credentials(
                $user,
                $pass,
                $clientId,
                $secret,
                'https://api.snapppay.ir/'
            );
        }
        $this->setting = $snappPaySetting;
    }
    public function processPayment(int $amount,array $data) {
        $snappPay = new SnappPay($this->setting);
        $order = new Order($data['id'], $amount, $amount, $data['deliveryAmount'], 0, Currency::RIAL, $data['phone']);
        $category = new ProductCategory('Normal', 1);
        $orderProduct1 = new OrderProduct($data['productId'], $data['name'], $data['amount'], $data['amount'], $data['qty'], $category);
        $order->addProduct($orderProduct1);
        // \Log::info($order->getPrice());
        // \Log::info(Currency::RIAL);
        $merchantEligible = $snappPay->isMerchantEligible($order->getPrice(), Currency::RIAL);
        // \Log::info($merchantEligible);
        if ($merchantEligible['successful'] &&
            isset($merchantEligible['response']['eligible']) &&
            $merchantEligible['response']['eligible']) {

            // Get paymentToken
            // in this sectoin we must get the payment token from the snapp pay service and save this token for verify or revert the transaction.
            // then the user will redirect to the snapp pay service and website for pay
            // be careful that the rand(1,50000) parameter that i used on the below line is the transactionId that should be unique and send to the snapp pay service

            $paymentToken = $snappPay->getPaymentToken($order, $data['callback'].$order->getId(), rand(1,50000));
            // \Log::info($paymentToken);
            //in this section we check the status of the payment token api response
            if ($paymentToken['successful'] &&
                isset($paymentToken['response']['paymentToken'])) {

                // Save paymentToken in order .
                // in fact you should add the 'paymentToken' column and save this token for order that we can send this token for verify or revert the transaction
                $order->setPaymentToken($paymentToken['response']['paymentToken']);

                return [
                    'data' => [
                        'token'=>$paymentToken['response']['paymentToken'],
                        'payment_amount'=>$order->getPrice(),
                        'paymentPageUrl'=>$paymentToken['response']['paymentPageUrl']
                    ],
                    'status' => 1,
                ];
            }
            return ['status'=>-1, 'Payment Token:' => $paymentToken];
        }

        return ['status'=>-1, 'merchantEligible: ' => $merchantEligible];

    }
    public function verifyPayment(array $data) {
        $snappPay = new SnappPay($this->setting);
        try {
            $resultVerify = $snappPay->verifyOrder($data['paymentToken']);
        } catch (\Exception $exception) {
            // \Log::info($exception);
            $resultRevert  = $snappPay->revertOrder($data['paymentToken']);
            return ["status"=>-1];
        }
        // \Log::info($resultVerify);
        if($resultVerify['successful'] && isset($resultVerify['response']['transactionId'])) {
            $resultSettle = $snappPay->settleOrder($data['paymentToken']);
            if($resultSettle['successful'] && isset($resultSettle['response']['transactionId'])) {
                return ['data'=>['token'=>$resultSettle['response']['transactionId']],'status'=>1];
            }
        }
        return ["status"=>-1];

    }
    public function cancelPayment(array $data) {
        $snappPay = new SnappPay($this->setting);
        $resultCancel = ["status"=>-1];
        try {
            $resultCancel = $snappPay->cancelOrder($data['paymentToken']);
        } catch (\Exception $exception) {
            // \Log::info($exception);
            return ["status"=>-1];
        }
        return $resultCancel;
    }
    public function deletePayment(string $paymentToken) {
        $snappPay = new SnappPay();
        $resultCancel = $snappPay->cancelOrder($paymentToken);
        if($resultCancel['successful'] && isset($resultCancel['response']['transactionId'])) {
            return $resultCancel['response'];
        }
        return ["status"=>-1];
    }
}
