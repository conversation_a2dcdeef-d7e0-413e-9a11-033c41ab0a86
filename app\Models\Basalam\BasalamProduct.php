<?php

namespace App\Models\Basalam;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BasalamProduct extends Model
{
    use HasFactory;

    protected $table = 'basalam_products';

    protected $fillable = [
        'product_id',
        'product_unique_time',
        'name',
        'description',
        'price',
        'photo_medium',
        'average_rating',
        'rating_count',
        'status',
        'owner_name',
        'owner_city',
        'owner_id',
        'vendor_identifier',
        'has_delivery',
        'is_available',
        'can_add_to_cart',
        'preparation_days',
        'stock',
        'weight',
    ];
}
