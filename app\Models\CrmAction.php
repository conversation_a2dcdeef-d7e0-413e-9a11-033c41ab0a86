<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CrmAction extends Model
{
    use HasFactory;

    public $timestamps = false;
    public const USER_REGISTER = 'USER_REGISTER';
    public const PAGE_EXPIRED = 'PAGE_EXPIRED';
    public const PAGE_2_DAYS_TO_EXPIRE = 'PAGE_2_DAYS_TO_EXPIRE';
    public const PAGE_7_DAYS_TO_EXPIRE = 'PAGE_7_DAYS_TO_EXPIRE';
    public const TRANSACTION_APPROVED = 'TRANSACTION_APPROVED';
    public const TRANSACTION_WAITING = 'TRANSACTION_WAITING';

    public function tracks(): HasMany
    {
        return $this->hasMany(CrmTrack::class);
    }
}
