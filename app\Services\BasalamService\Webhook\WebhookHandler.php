<?php

namespace App\Services\BasalamService\Webhook;

use App\Services\BasalamService\Messengers\MessageService;
use App\Services\BasalamService\Messengers\PostbackService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WebhookHandler
{
    private PostbackService $postbackService;
    private MessageService $messageService;
    private WebhookExtractor $extractor;

    public string $webhookType;
    public string $webhookData;

	public function __construct(
		PostbackService  $postbackService,
		MessageService   $messageService,
		WebhookExtractor $extractor,
	)
	{
		$this->postbackService = $postbackService;
		$this->messageService = $messageService;
		$this->extractor = $extractor;
	}

    public function isEcho(Request $request): bool
    {
        return $request['entry'][0]['messaging'][0]['message']['is_echo'] ?? false;
    }

    public function isRead(Request $request): bool
    {
        return isset($request['entry'][0]['messaging'][0]['read']);
    }

    public function isPostback(Request $request): bool
    {
        return $this->postbackService->isPostBack($request);
    }

    public function extractWebhook(Request $request): void
    {
        $result = $this->extractor->extract($request->all());

        if ($result) {
            $this->setWebhookType($result['type']);
            $this->setWebhookData($result);
        } else {
            $this->setWebhookType("invalid");
            $this->setWebhookData(['text' => "invalid"]);
        }
    }

    private function setWebhookData(array $data): void
    {
        if (isset($data['url'])) {
            $this->webhookData = $data['url'];
        } elseif (isset($data['text'])) {
            $this->webhookData = $data['text'];
        }
    }

    private function setWebhookType(string $type): void
    {
        $this->webhookType = $type;
    }

    public function getSenderId(Request $request): string
    {
        return $request['entry'][0]['messaging'][0]['sender']['id'];
    }

    public function handleInvalidFormatMessage(string $senderId): void
    {
        switch ($this->webhookType) {
            case 'video':
                $this->sendInvalidVideoMessage($senderId);
                break;

            case 'reels':
                $this->sendInvalidReelsMessage($senderId);
                break;

            case 'audio':
                $this->sendInvalidAudioMessage($senderId);
                break;

            case 'images':
                $this->sendInvalidMultipleImagesMessage($senderId);
                break;

            case 'text':
//                $this->sendInvalidFormatMessage($senderId);
                break  ;

            default:
                $this->sendInvalidFormatMessage($senderId);
                break;
        }
    }

    private function sendInvalidReelsMessage(string $senderId): void
    {
        $this->messageService->sendTextMessage(
            $senderId,
            'شما یک Reels ارسال کردید ❌
لطفا یک عکس یا پست حاوی عکس ارسال کنید ✅'
        );
    }

    private function sendInvalidVideoMessage(string $senderId): void
    {
        $this->messageService->sendTextMessage(
            $senderId,
            'شما یک ویدیو ارسال کردید ❌
لطفا یک عکس یا پست حاوی عکس ارسال کنید ✅'
        );
    }

    private function sendInvalidAudioMessage(string $senderId): void
    {
        $this->messageService->sendTextMessage(
            $senderId,
            'شما یک voice ارسال کردید ❌
لطفا یک عکس یا پست حاوی عکس ارسال کنید ✅'
        );
    }

    private function sendInvalidMultipleImagesMessage(string $senderId): void
    {
        $this->messageService->sendTextMessage(
            $senderId,
            'شما یک کالکشن از عکس ارسال کردید ❌
لطفا عکس ها را یکی یکی ارسال کنید ✅'
        );
    }

    private function sendInvalidFormatMessage(string $senderId): void
    {
        $this->messageService->sendTextMessage($senderId, 'خطا در دریافت محصولات! لطفا یک عکس معتبر یا نام محصول ارسال کنید.');
    }

    public function sendWaitingMessageForImage(string $senderId): void
    {
        $logValue = $this->messageService->sendTextMessage($senderId, 'داریم دنبال دقیق‌ترین نتایج برات میگردیم. یکمی صبر کن... 🕵️‍♂️');
        Log::info(json_encode($logValue));
    }

	public function sendWaitingMessageForText(string $senderId, string $product): void
	{
		$message = 'ظاهرا دنبال '."'$product'".' میگردی'."\n";
		$message .= 'ما هم داریم دنبال دقیق‌ترین نتایج برات میگردیم. یکمی صبر کن... 🕵️‍♂️';
		$logValue = $this->messageService->sendTextMessage($senderId, $message);
		Log::info(json_encode($logValue));
	}
}
