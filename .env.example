APP_NAME=
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE=Asia/Tehran
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

MONGODB_URI=
MONGODB_DB=

DB_CONNECTION=
DB_HOST=   
DB_PORT=
DB_DATABASE=
DB_USERNAME=
DB_PASSWORD=

SESSION_DRIVER=file
SESSION_LIFETIME=120

SNAPPPAY_USERNAME=
SNAPPPAY_PASSWORD=
SNAPPPAY_CLIENT_ID=
SNAPPPAY_CLIENT_SECRET=
SNAPPPAY_STAGING_URL=

SCOUT_DRIVER=
MEILISEARCH_HOST=
MEILISEARCH_KEY=

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=file
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# SAPAK SMS Service Configuration
SAPAK_API_KEY=
SAPAK_BASE_URL=
SAPAK_FROM=

OPEN_AI_BASE_URL=

SHOP_AI_BASE_URL=
SHOP_AI_API_KEY=


IDEA_API_URL=

PRIVATE_KEY_PATH=
PUBLIC_KEY_PATH=

PRIVATE_KEY_PASSWORD=

SAPAK_API_KEY=
SAPAK_BASE_URL=
SAPAK_FROM=

API_KEY=