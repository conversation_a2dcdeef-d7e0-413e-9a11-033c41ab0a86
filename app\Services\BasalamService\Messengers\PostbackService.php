<?php

namespace App\Services\BasalamService\Messengers;

use App\Services\BasalamService\Product\ProductService;
use App\Services\BasalamService\Review\ReviewService;
use Exception;
use Illuminate\Http\Request;

/**
 *  Class PostbackService
 *
 *  This service is responsible for handling postback events from users interacting with a chatbot
 *  on Instagram platforms.
 *
 *  The main functionality of this service is to process postback data and
 *  send the relevant message when a user requests it.
 */
class PostbackService
{

    /**
     * Service responsible for retrieving and formatting product details.
     *
     * @var ProductService
     */
    protected ProductService $productService;

    /**
     * Service responsible for sending messages to users via the messaging platform.
     *
     * @var MessageService
     */
    protected MessageService $messageService;

    /**
     * Service responsible for handling review-related operations.
     *
     * @var ReviewService
     */
    protected ReviewService $reviewService;

    /**
     * Constructs a new instance of PostbackService.
     *
     * @param ProductService $productService Service for handling product-related operations.
     * @param MessageService $messageService Service for sending user messages.
     * @param ReviewService $reviewService   Service for handling review logic.
     */
    public function __construct(ProductService $productService,
                                MessageService $messageService,
                                ReviewService $reviewService,)
    {
        $this->productService = $productService;
        $this->messageService = $messageService;
        $this->reviewService = $reviewService;
    }

    /**
     * Handles an incoming postback request and processes it based on the action type.
     *
     * This method determines whether the postback corresponds to a product description or a review-related action.
     * - For product descriptions, it fetches the product details and sends them to the user.
     * - For reviews, it determines the review type and delegates handling to the review service.
     *
     * @param Request $request The incoming HTTP request containing the postback payload.
     * @return bool True if the postback was handled successfully, false otherwise.
     */
    public function handle(Request $request): bool
    {
        $postback = $this->getPostback($request->all());

        if ($postback) {
            if ($this->isDescriptionPostback($postback)) {

                $recipientId = $this->getRecipientId($request);
                $this->sendDescription($recipientId, $postback);

                return true;
            }

            if ($this->isReviewPostback($postback)) {

                $recipientId = $this->getRecipientId($request);
                $this->reviewService->createReview($recipientId, $postback);

                return true;
            }
        }

        return false;
    }

    /**
     * Extracts the postback payload from the incoming request data.
     *
     * This method searches through the request's data structure to find the postback payload.
     * If a valid payload is found, it returns it; otherwise, it returns null.
     *
     * @param array $data The raw request data.
     * @return string|null The postback payload if found, or null if no valid postback is present.
     */
    public function getPostback(array $data): ?string
    {

        if (isset($data['object']) && $data['object'] === 'instagram') {
            if (isset($data['entry']) && is_array($data['entry'])) {
                foreach ($data['entry'] as $entry) {
                    if (isset($entry['messaging']) && is_array($entry['messaging'])) {
                        foreach ($entry['messaging'] as $messageData) {
                            if (isset($messageData['postback']) && is_array($messageData['postback'])) {
                                if (isset($messageData['postback']['payload'])) {
                                    return $messageData['postback']['payload'];
                                }
                            }
                        }
                    }
                }
            }
        }

        return null;
    }

    /**
     * Checks if the incoming request contains a postback payload.
     *
     * This method checks whether the request data includes a valid postback payload.
     * If no postback payload is found, it returns false; otherwise, it returns true.
     *
     * @param Request $request The incoming HTTP request to be analyzed.
     * @return bool True if the request contains a valid postback payload, false otherwise.
     */
    public function isPostBack(Request $request): bool
    {
        if ($this->getPostback($request->all()) == null) {
            return false;
        }
        return true;
    }

    /**
     * Determines whether the postback corresponds to a product description request.
     *
     * This method checks if the postback payload starts with the "Details_" prefix, indicating that
     * the user is requesting the details of a product.
     *
     * @param string $postback The postback payload to check.
     * @return bool True if the postback corresponds to a product description request, false otherwise.
     */
    private function isDescriptionPostback(string $postback): bool
    {
        return str_starts_with($postback,'Details_');
    }

    /**
     * Determines whether the postback corresponds to a review request.
     *
     * @param string $postback The postback payload to check.
     * @return bool True if the postback corresponds to a review request, false otherwise.
     */
    private function isReviewPostback(string $postback): bool
    {
        try {
            $this->reviewService->validateReviewPayload($postback);
            return true;
        } catch (Exception $exception) {
            return false;
        }
    }


    /**
     * Sends the product description message to the user.
     *
     * This method retrieves the product's description based on the postback payload, formats
     * the description message, and sends it to the user via the MessageService.
     *
     * @param string $recipientId The unique ID of the recipient (user).
     * @param string $postback The postback payload containing the product ID.
     * @return void
     */
    private function sendDescription(string $recipientId, string $postback): void
    {
        preg_match('/Details_(\d+)/', $postback, $matches);

        $message = $this->productService->getDescriptionMessage($matches[1]);
        $link = $this->productService->getProductLink($matches[1]);

        $this->messageService->sendMessageWithButton($recipientId, $message, $link);
    }

    /**
     * Extracts the recipient's unique ID from the incoming request.
     */
    private function getRecipientId($request)
    {
        return $request['entry'][0]['messaging'][0]['sender']['id'];
    }

    /**
     * Determines the review type based on the postback payload.
     *
     * This method checks if the postback payload starts with the "Review_" prefix and identifies
     * the corresponding review type (not_good, not_bad, perfect) based on the suffix value.
     *
     * @param string $postback The postback payload to check
     * @return string The review type ("not_good", "not_bad", "perfect")
     */
    private function getReviewTypeFromPostback(string $postback): string
    {
        $value = substr($postback, strlen('Review_'));
        return match ($value) {
            '1' => 'not_good',
            '2' => 'not_bad',
            '3' => 'perfect',
        };
    }
}
