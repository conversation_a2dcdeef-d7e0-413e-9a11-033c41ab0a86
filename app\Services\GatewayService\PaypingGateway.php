<?php

namespace App\Services\GatewayService;

use App\Contracts\Gateway\PaymentGateway;
use Illuminate\Support\Facades\Http;

class PaypingGateway implements PaymentGateway
{
    private $APIKey;
    public function __construct(string $apiKey) {
        $this->APIKey = $apiKey;
    }
    public function processPayment(int $amount,array $data) {
        $payload = [
            "amount"=> $amount,
            "payerIdentity"=> $data->payerIdentity,
            "payerName"=> $data->payerName,
            "description"=> $data->description,
            "returnUrl"=> $data->returnUrl,
            "clientRefId"=> $data->clientRefId
        ];
        $response = Http::withHeaders([
            'Authorization' => $this->APIKey
        ])->post('https://api.payping.ir/v2/pay', $payload);
        return $response;

    }
    public function verifyPayment(int $amount,array $data) {
        $payload = [
            "amount"=> $amount,
            "refId"=> $data['refId']
        ];
        $response = Http::withHeaders([
            'Authorization' => $this->APIKey
        ])->post('https://api.payping.ir/v2/pay/verify', $payload);
        return $response;

    }
    public function deletePayment(string $code) {
        $response = Http::withHeaders([
            'Authorization' => $this->APIKey
        ])->delete('https://api.payping.ir/v2/pay/'. $code);
        return $response;

    }
}
