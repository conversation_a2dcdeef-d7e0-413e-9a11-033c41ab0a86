<?php

namespace App\Services\Webservice\Abstract;

use App\Contracts\Webservice\InstagramUserInfoFetcherInterface;
use App\Models\Webservice\SpecialUser;
use App\Services\TelegramService;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

abstract class AbstractInstagramUserInfoService implements InstagramUserInfoFetcherInterface
{
    protected string $fieldType;

    /**
     * Fetch user information from the Instagram API.
     *
     * @param SpecialUser $user
     * @param string $userId
     * @return array
     */
    public function fetchUserInfo(SpecialUser $user, string $userId): array
    {
        $url = "https://graph.instagram.com/v22.0/{$userId}?fields=" .
            $this->fieldType .
            "&access_token={$user->access_token}";

        $response = $this->sendRequest($url);
        return $this->extractData($response);
    }

    /**
     * Sends the HTTP GET request to the Instagram API.
     *
     * @param string $url
     * @return Response
     */
    protected function sendRequest(string $url): Response
    {
        return Http::get($url);
    }

    /**
     * Extracts the requested data from the API response.
     *
     * @param Response $response
     * @return array
     */
    protected function extractData(Response $response): array
    {
        return $response->json() ?? [];
    }
}
