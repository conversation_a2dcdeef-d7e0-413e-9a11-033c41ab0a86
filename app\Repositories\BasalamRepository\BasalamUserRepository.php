<?php

namespace App\Repositories\BasalamRepository;

use App\Models\Basalam\BasalamUser;
use App\Services\BasalamService\Enums\MessageTypeEnum;
use Illuminate\Database\Eloquent\Builder;

class BasalamUserRepository
{
	/**
	 * Creates a new user with the given Instagram ID.
	 *
	 * @param string $instagramId The Instagram ID of the new user.
	 * @return void
	 */
	public function createUser(string $instagramId): void
	{
		BasalamUser::create([
			'instagram_id' => $instagramId,
		]);
	}

	/**
	 * Increments the message count for a user based on their Instagram ID.
	 *
	 * @param string $instagramId The Instagram ID of the user.
	 * @return void
	 */
	public function incrementMessageCount(string $instagramId): void
	{
		$user = $this->getUserByInstagramId($instagramId);

		if ($user) {
			$user->message_count = $user->message_count + 1;
			$user->save();
		}
	}

	/**
	 * Retrieves a user from the database based on their Instagram ID.
	 *
	 * @param string $instagramId The Instagram ID of the user.
	 * @return BasalamUser|null Returns the user if found, null otherwise.
	 */
	public function getUserByInstagramId(string $instagramId): ?BasalamUser
	{
		return BasalamUser::where('instagram_id', $instagramId)->first();
	}

	/**
	 * Add a new image message for found user based on Instagram ID
	 * @param string $instagramId
	 * @param string $imageUrl
	 * @return int
	 */
	public function addImageMessage(string $instagramId, string $imageUrl): int
	{
		$user = $this->getUserByInstagramId($instagramId);

		if ($user === null)
			return -1;

		$message = $user->messages()->create([
			'url'  => $imageUrl,
			'type' => MessageTypeEnum::IMAGE->value,
		]);

		return $message->id;
	}

	/**
	 * Add a new text message for found user based on Instagram ID
	 * @param string $instagramId
	 * @param string $query
	 * @return int
	 */
	public function addTextMessage(string $instagramId, string $query): int
	{
		$user = $this->getUserByInstagramId($instagramId);

		if ($user === null)
			return -1;

		$message = $user->messages()->create([
			'query' => $query,
			'type'  => MessageTypeEnum::TEXT->value,
		]);

		return $message->id;
	}

	public function getUsersWithLatestUnprocessedImageMessage(): Builder
	{
		return BasalamUser::query()
			->whereHas('messages', function ($query) {
				$query
					->where('is_processed', false)
					->where('type', MessageTypeEnum::IMAGE->value)
					->where('created_at', '>', now()->subHours(23)->subMinutes(30))
					->whereIn('id', function ($query) {
						$query
							->selectRaw('max(id)')
							->from('basalam_messages')
							->groupBy('user_id');
					});
			});
	}
}
