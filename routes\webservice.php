<?php
/*
|--------------------------------------------------------------------------
| Webservice API Routes
|--------------------------------------------------------------------------
|
| This file defines the API routes for handling web service interactions.
| It includes routes for sending messages via the Messenger API and
| fetching user information from Instagram.
|
| All routes are protected by API exception handling middleware and
| rate-limiting to ensure stability and security.
|
*/

use App\Http\Controllers\Webservice\InstagramUser\InstagramFollowCountController;
use App\Http\Controllers\Webservice\InstagramUser\InstagramFollowStatusController;
use App\Http\Controllers\Webservice\InstagramUser\InstagramUsernameController;
use App\Http\Controllers\Webservice\Messenger\AudioMessageController;
use App\Http\Controllers\Webservice\Messenger\ButtonTextMessageController;
use App\Http\Controllers\Webservice\Messenger\GenericTemplateMessageController;
use App\Http\Controllers\Webservice\Messenger\PhotoMessageController;
use App\Http\Controllers\Webservice\Messenger\QuickReplyMessageController;
use App\Http\Controllers\Webservice\Messenger\VideoMessageController;
use App\Http\Controllers\Webservice\Messenger\TextMessageController;
use App\Http\Middleware\Webservice\CloudflareProxies as RealIp;
use App\Http\Middleware\Webservice\HandleApiExceptions as Webservice;
use Illuminate\Support\Facades\Route;

Route::middleware([RealIp::class, Webservice::class, 'throttle:webservice_throttle'])->group(function () {

    /*
    |--------------------------------------------------------------------------
    | Messenger API Routes
    |--------------------------------------------------------------------------
    |
    | These routes handle the sending of different types of messages through
    | the Messenger API. Supported message types include text, media,
    | buttons, quick replies, and generic templates.
    |
    | Requests must be authenticated with a valid API token, and rate limits
    | are enforced to prevent abuse.
    |
    */
    Route::prefix('send')->group(function () {
        Route::post('/text', [TextMessageController::class, 'send']);
        Route::post('/photo', [PhotoMessageController::class, 'send']);
        Route::post('/video', [VideoMessageController::class, 'send']);
        Route::post('/audio', [AudioMessageController::class, 'send']);
	    Route::post('/button-text', [ButtonTextMessageController::class, 'send']);
	    Route::post('/quick-reply', [QuickReplyMessageController::class, 'send']);
	    Route::post('/generic-template', [GenericTemplateMessageController::class, 'send']);
    });

    /*
    |--------------------------------------------------------------------------
    | Instagram User API Routes
    |--------------------------------------------------------------------------
    |
    | These routes handle fetching specific user information from Instagram.
    | Users can retrieve their username, follower count, and follow status.
    |
    | Requests must be authenticated with a valid API token, and rate limits
    | are enforced to prevent abuse.
    |
    */
    Route::prefix('instagram-user')->group(function () {
        Route::post('/username', [InstagramUsernameController::class, 'fetchInfo']);
        Route::post('/follow-count', [InstagramFollowCountController::class, 'fetchInfo']);
        Route::post('/follow-status', [InstagramFollowStatusController::class, 'fetchInfo']);
    });
});
