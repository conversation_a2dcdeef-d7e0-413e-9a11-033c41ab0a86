<?php

namespace App\Models\Basalam;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class BasalamMessage extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'basalam_messages';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
	protected $fillable = [
		'category_id',
		'brand_id',
		'url',
		'query',
		'type',
		'is_processed',
		'user_id',
	];

	/**
	 * Get the Basalam category who owns the message.
	 *
	 * @return BelongsTo
	 */
	public function category(): BelongsTo
	{
		return $this->belongsTo(BasalamCategory::class);
	}

	/**
	 * Get the Basalam brand who owns the message.
	 *
	 * @return BelongsTo
	 */
	public function brand(): BelongsTo
	{
		return $this->belongsTo(BasalamBrand::class);
	}

    /**
     * Get the Basalam user who owns the message.
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(BasalamUser::class);
    }

    /**
     * Get the review related to the message
     *
     * @return HasOne
     */
    public function review(): HasOne
    {
        return $this->hasOne(BasalamReview::class);
    }
}
