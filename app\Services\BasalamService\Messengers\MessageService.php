<?php

namespace App\Services\BasalamService\Messengers;

use App\Repositories\BasalamRepository\BasalamAdminRepository;
use Illuminate\Support\Facades\Http;

/**
 *  Class MessageService
 *
 *  This Service is responsible for sending messages to users via the Instagram messaging API.
 *
 *  It interacts with the BasalamAdminRepository to retrieve the admin's access token and formats
 *  the request payload before sending it to a specified endpoint for message delivery.
 */
class MessageService
{
    private BasalamAdminRepository $admin;
    private string $metaUrl;

    public function __construct(BasalamAdminRepository $adminRepository)
    {
        $this->admin = $adminRepository;
        $this->metaUrl = "https://graph.instagram.com/v20.0/me/messages?access_token=";
    }

    public function sendTextMessage($id, $message): array
    {
        $payload = ['recipient'=>['id'=>$id],'message'=>['text'=>$message]];

        return $this->sendMessage($payload);
    }

    public function sendMessageWithButton($id, $message, $link): array
    {
        $payload = ['recipient'=>['id'=>$id],'message'=>[
            'attachment'=>[
                'type'=>'template',
                'payload'=>[
                    'template_type'=>'button',
                    'text'=> $message,
                    'buttons'=>[
                        [
                            'type' =>"web_url",
                            'url'  => $link,
                            'title'=> "خرید کالا",
                            'webview_height_ratio'=> 'full'
                        ]
                    ]
                ]
            ]
        ]];

        return $this->sendMessage($payload);
    }

    public function sendReviewButton($receiverId, $messageId): array
    {

        $payload = ['recipient'=>['id'=>$receiverId],'message'=>[
            'attachment'=>[
                'type'=>'template',
                'payload'=>[
                    'template_type'=>'button',
                    'text'=> '❓ ۱۰ تا محصول مشابه در پیام بالا برات ارسال شد👆👆

از پیشنهادها راضی بودی‌؟',
                    'buttons'=>[
                        [
                            "type"    => "postback",
                            "title"   => "😍 آره عالی",
                            "payload" => json_encode(["message_id" => $messageId, "score" => 3])
                        ],
                        [
                            "type"    => "postback",
                            "title"   => "🧐 بد نبود",
                            "payload" => json_encode(["message_id" => $messageId, "score" => 2])
                        ],
                        [
                            "type"    => "postback",
                            "title"   => "😒 خوب نبود",
                            "payload" => json_encode(["message_id" => $messageId, "score" => 1])
                        ]
                    ]
                ]
            ]
        ]];

        return $this->sendMessage($payload);
    }

    public function sendCollection($id, $elements)
    {
        $payload = [
            'recipient'=>['id'=>$id],
            'message'=>[ 'attachment' => [
                'type'=>'template',
                'payload'=>[
                    'template_type'=>'generic',
                    'elements'=>$elements
                ]
            ]]
        ];

        $this->sendMessage($payload);
    }

    private function sendMessage($messagePayload): array
    {
        $access_token = $this->admin->getAdminToken();

        $url = $this->metaUrl . $access_token;

        try {
            $response = Http::withHeaders(['Content-Type' => 'application/json'])
                ->timeout(20)
                ->post($url, $messagePayload);

            if ($response->successful()) {
                return [
                    'status'  => 'success',
                    'message' => 'Request successful',
                    'data'    => $response->body(),
                ];
            }

            return [
                'status'    => 'error',
                'message'   => 'Request failed',
                'data'      => $response->body(),
                'http_code' => $response->status(),
            ];

        } catch (\Exception $e) {
            return [
                'status'  => 'error',
                'message' => 'Exception: ' . $e->getMessage(),
                'data'    => null,
            ];
        }
    }
}
