<?php

namespace App\Services\Webservice\MessageService;

use App\Services\Webservice\Abstract\AbstractMessageService;

class GenericTemplateMessageService extends AbstractMessageService
{
	protected string $attachmentType = 'template';

	/**
	 * Gets the message content for a text message.
	 *
	 * @param array $messageData
	 * @return array
	 */
	protected function getMessageContent(array $messageData): array
	{
		return [
			'attachment' => [
				'type'    => $this->attachmentType,
				'payload' => [
					'template_type' => 'generic',
					'elements'      => $messageData['elements'],
				],
			],
		];
	}
}
