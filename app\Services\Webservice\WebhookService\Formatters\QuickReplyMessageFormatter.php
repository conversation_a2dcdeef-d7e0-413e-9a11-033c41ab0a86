<?php

namespace App\Services\Webservice\WebhookService\Formatters;

use App\Contracts\Webservice\WebhookFormatter;

class QuickReplyMessageFormatter implements WebhookFormatter
{
	public function format(array $message): array
	{
		return [
			'sender'       => $message['sender']['id'],
			'receiver'     => $message['recipient']['id'],
			'message_type' => 'quick_reply',
			'text'         => $message['message']['text'],
			'payload'      => $message['message']['quick_reply']['payload'],
            'timestamp' => gmdate('Y-m-d\TH:i:s\Z', intdiv((int)$message['timestamp'], 1000)),
            'is_admin'     => isset($message['message']['is_echo']),
		];
	}
}
