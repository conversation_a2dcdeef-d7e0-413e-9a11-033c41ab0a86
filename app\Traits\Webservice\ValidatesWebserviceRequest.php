<?php

namespace App\Traits\Webservice;

use App\Exceptions\Webservice\InvalidApiTokenException;
use App\Exceptions\Webservice\InvalidEndpointException;
use App\Models\Webservice\SpecialUser;
use App\Services\Webservice\Validators\ApiTokenValidationService;
use App\Services\Webservice\Validators\ValidEndpointValidationService;
use Illuminate\Http\Request;

trait ValidatesWebserviceRequest
{
    /**
     * Validates the API token and the request origin.
     *
     * @param Request $request
     * @return SpecialUser
     * @throws InvalidEndpointException
     * @throws InvalidApiTokenException
     */
    protected function validateRequest(Request $request): SpecialUser
    {
        $apiToken = $request->header('api-token');
        $specialUser = ApiTokenValidationService::validateApiToken($apiToken);

        $requestOrigin = $request->ip();
        ValidEndpointValidationService::validateEndpoint($specialUser, $requestOrigin);

        return $specialUser;
    }
}
