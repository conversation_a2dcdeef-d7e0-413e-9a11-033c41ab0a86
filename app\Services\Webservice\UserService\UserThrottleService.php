<?php

namespace App\Services\Webservice\UserService;

use Illuminate\Support\Facades\Redis;
use App\Models\Webservice\SpecialUser as User;

class UserThrottleService
{
    /**
     * Retrieves the valid endpoints for a user based on their API token.
     *
     * @param string $apiToken
     * @return array|null
     */
    public static function getValidEndpointsByApiToken(string $apiToken): ?array
    {
        // Key for Redis
        $key = "user_valid_endpoints_{$apiToken}";

        // Step 1: Check Redis
        $cachedEndpoints = Redis::get($key);
        if ($cachedEndpoints) {
            return json_decode($cachedEndpoints, true);
        }

        // Step 2: Fallback to Database
        $specialUser = User::getUserByApiToken($apiToken);
        if ($specialUser) {
            $validEndpoints = json_decode($specialUser->valid_endpoints, true);

            // Step 3: Cache the result in Redis
            Redis::set($key, json_encode($validEndpoints), 'EX', 43200); // TTL: 12 hours
            return $validEndpoints;
        }

        return null;
    }
}
