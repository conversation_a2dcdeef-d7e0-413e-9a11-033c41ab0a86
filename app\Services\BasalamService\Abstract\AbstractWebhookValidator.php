<?php

namespace App\Services\BasalamService\Abstract;

abstract class AbstractWebhookValidator
{
    /**
     * Main validation logic to be implemented by child classes.
     *
     * @param array $data
     * @return string|null
     */
    abstract public function validate(array $data): ?string;

    /**
     * Helper to validate if a URL is an Instagram post URL.
     *
     * @param string $url
     * @return bool
     */
    protected function isInstagramPostUrl(string $url): bool
    {
        return preg_match('/https?:\/\/(www\.)?instagram\.com\/p\/[a-zA-Z0-9_-]+/', $url);
    }
}
