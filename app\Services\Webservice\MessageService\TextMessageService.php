<?php

namespace App\Services\Webservice\MessageService;

use App\Services\Webservice\Abstract\AbstractMessageService;

class TextMessageService extends AbstractMessageService
{
    protected string $attachmentType = 'text';

    /**
     * Gets the message content for a text message.
     *
     * @param array $messageData
     * @return array
     */
    protected function getMessageContent(array $messageData): array
    {
        return [
            'text' => $messageData['text'],
        ];
    }
}
