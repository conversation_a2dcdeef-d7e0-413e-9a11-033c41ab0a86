<?php

namespace App\DTOs;

use Illuminate\Http\Request;

class UpdateOrCreateManyMessageAgentDTO
{
    private function __construct(
        public readonly string $email,
        public readonly ?string $instaId,
    ) {}

    public static function fromRequest(Request $request): self
    {
        return new self(
            email: $request->input('email'),
            instaId: $request->input('insta_id'),
        );
    }
}
