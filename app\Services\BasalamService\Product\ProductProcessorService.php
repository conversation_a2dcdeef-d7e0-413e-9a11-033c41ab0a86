<?php

namespace App\Services\BasalamService\Product;

use App\Repositories\BasalamRepository\BasalamProductRepository;
use App\Services\BasalamService\ImageService;
use App\Services\BasalamService\Messengers\MessageService;
use App\Services\BasalamService\TextService;
use Illuminate\Database\Eloquent\Collection;
use Exception;
use Illuminate\Support\Facades\Log;

class ProductProcessorService
{
    private ImageService $imageService;
    private TextService $textService;
    private MessageService $messageService;
    private BasalamProductRepository $productRepository;

    private string $endpoint = "https://dmplus.manymessage.com/webhook/api/basalam/store-product";


	public function __construct(
		ImageService             $imageService,
		TextService              $textService,
		MessageService           $messageService,
		BasalamProductRepository $productRepository,
	)
	{
		$this->imageService = $imageService;
		$this->textService = $textService;
		$this->messageService = $messageService;
		$this->productRepository = $productRepository;
	}

	public function processImageBaSalam(string $senderId, string $imageUrl): bool
	{
		try {
			$result = $this->imageService->processImageBaSalam($imageUrl, $this->endpoint);

			[$productIds, $productTime] = $this->extractProductDetails($result);

			$products = $this->fetchProducts($productIds, $productTime);

			$elements = $this->prepareProductElementsBaSalam($products);

			$this->messageService->sendTextMessage($senderId, 'نتایج از باسلام:');

			$this->messageService->sendCollection($senderId, $elements);

			return true;
		} catch (Exception $exception) {
			Log::error("***************** BASALAM IMAGE ******************");
			Log::error($exception->getMessage());

			return false;
		}
	}

	public function processImageTorob(string $senderId, string $imageUrl): bool
	{
		try {
			$endpoint = 'http://185.215.244.247:8080/upload';

			$result = $this->imageService->processImageTorob($imageUrl, $endpoint);

			$decodedResult = json_decode($result, true);

			$products = $decodedResult['results'];

			$elements = $this->prepareProductElementsTorob($products);

			$this->messageService->sendTextMessage($senderId, 'نتایج از ترب:');

			$this->messageService->sendCollection($senderId, $elements);

			return true;
		} catch (Exception $e) {
			Log::error("***************** TOROB IMAGE ******************");
			Log::error($e->getMessage());

			return false;
		}
	}

	public function processTextBaSalam(string $senderId, string $product): bool
	{
		try {
			$result = $this->textService->processTextBaSalam($product, $this->endpoint);

			[$productIds, $productTime] = $this->extractProductDetails($result);

			$products = $this->fetchProducts($productIds, $productTime);

			$elements = $this->prepareProductElementsBaSalam($products);

			$this->messageService->sendTextMessage($senderId, 'نتایج از باسلام:');

			$this->messageService->sendCollection($senderId, $elements);

			return true;
		} catch (Exception $exception) {
			Log::error("***************** BASALAM TEXT ******************");
			Log::error($exception->getMessage());

			return false;
		}
	}

	public function processTextTorob(string $senderId, string $product): bool
	{
		try {
			$endpoint = 'http://185.215.244.247:8080/search';

			$result = $this->textService->processTextTorob($product, $endpoint);

			$decodedResult = json_decode($result, true);

			$products = $decodedResult['results'];

			$elements = $this->prepareProductElementsTorob($products);

			$this->messageService->sendTextMessage($senderId, 'نتایج از ترب:');

			$this->messageService->sendCollection($senderId, $elements);

			return true;
		} catch (Exception $e) {
			Log::error("***************** TOROB TEXT ******************");
			Log::error($e->getMessage());

			return false;
		}
	}

	public function sendReviewButton(string $senderId, int $messageId): void
	{
		$this->messageService->sendReviewButton($senderId, $messageId);
	}

    private function extractProductDetails(string $result): array
    {
        $decodedResult = json_decode($result, true);
        $productDetails = $decodedResult['product_details'];

        return [$productDetails['product_ids'], $productDetails['timestamp']];
    }

    private function fetchProducts(array $productIds, string $productTime): Collection
    {
        return $this->productRepository->getProductsByIdsAndTime($productIds, $productTime);
    }

	private function prepareProductElementsBaSalam(Collection $products): array
	{
		$elements = [];

		foreach ($products as $product)
			$elements[] = $this->createProductElementBaSalam($product);

		return $elements;
	}

	private function prepareProductElementsTorob(array $products): array
	{
		$elements = [];

		foreach ($products as $product)
			$elements[] = $this->createProductElementTorob($product);

		return $elements;
	}

    private function createProductElementBaSalam($product): array
    {
        $buttons = [
            [
                "type"    => "web_url",
                'url'     => "https://basalam.com/{$product->vendor_identifier}/product/{$product->product_id}?utm_source=instagram&utm_medium=directam",
                "title"   => "خرید",
                'webview_height_ratio'=> 'full'
            ],
            [
                "type" => "postback",
                "title" => "توضیحات بیشتر",
                "payload" => "Details_{$product->product_id}"
            ]
        ];

        $subTitle = $product->owner_city;
        $subTitle .= "\n قیمت ".number_format($product->price)." تومان";
        return [
            "subtitle"  => $subTitle,
            "title"     => $product->name,
            "image_url" => $product->photo_medium,
            "buttons"   => $buttons
        ];
    }

	private function createProductElementTorob(array $product): array
	{
		$buttons = [[
			'type'                 => 'web_url',
			'url'                  => $product['url'],
			'title'                => 'خرید',
			'webview_height_ratio' => 'full',
		]];

		return [
			'title'     => $product['name1'],
			'subtitle'  => 'قیمت: '.$product['price_text'],
			'image_url' => $product['media_urls'][0]['url'],
			'buttons'   => $buttons,
		];
	}
}
