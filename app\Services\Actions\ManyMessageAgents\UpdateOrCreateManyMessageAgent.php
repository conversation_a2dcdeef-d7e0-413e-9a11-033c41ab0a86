<?php

namespace App\Services\Actions\ManyMessageAgents;

use App\DTOs\UpdateOrCreateManyMessageAgentDTO;
use App\Models\ManymessageAgent;

class UpdateOrCreateManyMessageAgent
{
    public function handle(UpdateOrCreateManyMessageAgentDTO $dto): ManymessageAgent
    {
        $attributes = ['email' => $dto->email];
        $values = [];

        if ($dto->instaId !== null) {
            $values['insta_id'] = $dto->instaId;
        }

        $agent = ManymessageAgent::updateOrCreate($attributes, $values);

        return $agent;
    }
}
