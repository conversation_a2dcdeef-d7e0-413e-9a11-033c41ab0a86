<?php

namespace App\Http\Controllers\Webservice\Messenger;

use App\Exceptions\Webservice\InvalidApiTokenException;
use App\Exceptions\Webservice\InvalidEndpointException;
use App\Exceptions\Webservice\InvalidMessageTypeException;
use App\Http\Controllers\Webservice\Abstract\AbstractMessageController;
use App\Services\Webservice\MessageService\MessageService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class TextMessageController extends AbstractMessageController
{
    protected MessageService $messageService;

    public function __construct(MessageService $messageService)
    {
        $this->messageService = $messageService;
    }

    /**
     * Sends a text message.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws InvalidMessageTypeException
     * @throws InvalidEndpointException
     * @throws InvalidApiTokenException
     * @throws ValidationException
     */
    public function send(Request $request): JsonResponse
    {
        $specialUser = $this->validateRequest($request);

        $validator = Validator::make($request->all(), [
            'receiver_id' => 'required|string',
            'text'        => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors'  => $validator->errors(),
            ], 422);
        }

        $messageId = $this->messageService->sendMessage(
            $specialUser,
            $validator->validated(),
            'text'
        );

        return response()->json([
            'success'    => true,
            'message'    => 'Message sent successfully',
            'message_id' => $messageId
        ], status: 200);
    }
}
