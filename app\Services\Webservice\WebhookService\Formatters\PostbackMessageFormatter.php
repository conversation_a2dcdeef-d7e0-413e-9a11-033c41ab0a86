<?php

namespace App\Services\Webservice\WebhookService\Formatters;

use App\Contracts\Webservice\WebhookFormatter;

class PostbackMessageFormatter implements WebhookFormatter
{
	public function format(array $message): array
	{
		return [
			'sender'       => $message['sender']['id'],
			'receiver'     => $message['recipient']['id'],
			'message_type' => 'postback',
			'postback'     => [
				'title'   => $message['postback']['title'],
				'payload' => $message['postback']['payload'],
			],
            'timestamp' => gmdate('Y-m-d\TH:i:s\Z', intdiv((int)$message['timestamp'], 1000)),
            'is_admin'     => isset($message['message']['is_echo']),
		];
	}
}
