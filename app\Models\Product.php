<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Laravel\Scout\Searchable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory,Searchable,SoftDeletes;

    protected $table = 'products';

    protected $fillable = [
        'name',
        'description',
        'post_id',
        'price',
    ];

    public function getScoutKey(): mixed
    {
        return $this->id;
    }
    protected function makeAllSearchableUsing(Builder $query): Builder
    {
        return $query->with(['productGallery','user']);
    }
    public function getScoutKeyName(): mixed
    {
        return 'id';
    }
    public function toSearchableArray()
    {
        return [
            'id'=>$this->getKey(),
            'name' => $this->name,
            'description' => $this->description,
            'post_id'=> $this->post_id,
            'triggers'=> $this->triggers,
            'price'=> $this->price,
            'user_id'=> (int)$this->user_id,
            'created_at'=>$this->created_at->timestamp
        ];
    }

    public function productGallery(): HasMany
    {
        return $this->hasMany(ProductsGallery::class, 'product_id','id');
    }
    public function productPrices(): HasMany
    {
        return $this->hasMany(ProductPrice::class, 'product_id','id');
    }
    public function productMedia(): HasMany
    {
        return $this->hasMany(ProductsMedia::class, 'product_id','id');
    }
    public function orderAtrributes(): HasMany
    {
        return $this->hasMany(orderAttribute::class);
    }
    public function productAttribute(): HasMany
    {
        return $this->hasMany(productAttributes::class,'product_id','id');
    }
    public function orders(): HasMany
    {
        return $this->hasMany(order::class);
    }
    public function deliveryMethods(): BelongsToMany
    {
        return $this->belongsToMany(
            DeliveryMethod::class,
            'delivery_method_products',
            'products_id',
            'delivery_method_id'
        );
    }
    public function paymentMethods(): BelongsToMany
    {
        return $this->belongsToMany(
            PaymentMethod::class,
            'payment_method_products',
            'products_id',
            'payment_method_id'
        );
    }
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

}
