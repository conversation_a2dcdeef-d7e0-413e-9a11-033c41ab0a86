<?php

namespace App\Services\Webservice\InstagramUserService;

use App\Contracts\Webservice\InstagramUserInfoFetcherInterface;
use App\Models\Webservice\SpecialUser;
use App\Exceptions\Webservice\InvalidUserInfoTypeException;

class InstagramUserInfoService
{
    /**
     * An associative array of fetchers to dynamically manage different types of user info retrieval.
     *
     * @var InstagramUserInfoFetcherInterface[]
     */
    protected array $userInfoFetchers;

    /**
     * UserInfoService constructor.
     *
     * @param array $userInfoFetchers
     */
    public function __construct(array $userInfoFetchers)
    {
        $this->userInfoFetchers = $userInfoFetchers;
    }

    /**
     * Fetches user information based on requested fields.
     *
     * @param SpecialUser $user
     * @param string $userId
     * @param array $fields
     * @return array
     * @throws InvalidUserInfoTypeException
     */
    public function getUserInfo(SpecialUser $user, string $userId, array $fields): array
    {
        foreach ($fields as $field) {
            if (!isset($this->userInfoFetchers[$field])) {
                throw new InvalidUserInfoTypeException(field: $field);
            }
        }

        $response = [];
        foreach ($fields as $field) {
            $fetcher  = $this->userInfoFetchers[$field];
            $response = array_merge($response, $fetcher->fetchUserInfo($user, $userId));
        }

        return $response;
    }
}
