<?php

namespace App\Http\Controllers\Webservice;

use App\Http\Controllers\Controller;
use App\Models\Webservice\SpecialUser;
use App\Services\TelegramService;
use App\Services\Webservice\WebhookService\WebhookExtractor;
use App\Services\Webservice\WebhookService\WebhookSender;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class WebhookController extends Controller
{
    protected WebhookExtractor $webhookExtractor;
    protected WebhookSender $webhookSender;

    public function __construct(WebhookExtractor $webhookExtractor, WebhookSender $webhookSender)
    {
        $this->webhookExtractor = $webhookExtractor;
        $this->webhookSender = $webhookSender;
    }

    /**
     * Handle incoming webhook requests.
     *
     * This method extracts the webhook data and sends it to all valid endpoints of the associated SpecialUser.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws ConnectionException
     */
    public function handle(Request $request): JsonResponse
    {
        $formattedWebhook = $this->webhookExtractor->extract($request->all());
        if (!$formattedWebhook) return response()->json(['status' => 'success'], 200);

        if ($formattedWebhook['is_admin'] == true) {
            $instagramId = $formattedWebhook['sender'];
        } else {
            $instagramId = $formattedWebhook['receiver'];
        }

        $specialUser = SpecialUser::where('instagram_id', $instagramId)->first();

        if (!$specialUser) {
            return response()->json(['status' => 'error', 'message' => 'SpecialUser not found'], 404);
        }

        $this->webhookSender->sendWebhook($specialUser, $formattedWebhook);

        return response()->json(['status' => 'success'], 200);
    }
}
