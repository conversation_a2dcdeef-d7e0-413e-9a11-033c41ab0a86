<?php

namespace App\Services\Webservice\MessageService;

use App\Services\Webservice\Abstract\AbstractMessageService;

class AudioMessageService extends AbstractMessageService
{
    protected string $attachmentType = 'audio';

    /**
     * Gets the message content for an audio message.
     *
     * @param array $messageData
     * @return array
     */
    protected function getMessageContent(array $messageData): array
    {
        return [
            'attachment' => [
                'type' => $this->attachmentType,
                'payload' => [
                    'url' => $messageData['audio_url'], // Extract audio file URL
                ],
            ],
        ];
    }
}
