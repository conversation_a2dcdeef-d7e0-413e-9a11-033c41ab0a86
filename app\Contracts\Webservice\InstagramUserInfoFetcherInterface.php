<?php

namespace App\Contracts\Webservice;

use App\Exceptions\Webservice\InvalidUserInfoTypeException;
use App\Models\Webservice\SpecialUser;

/**
 * Interface InstagramUserInfoFetcherInterface
 *
 * This interface defines methods for retrieving specific user-related information
 * from the Instagram API. Implementing classes should fetch data such as usernames,
 * follower counts, and follow status based on the requested fields.
 */
interface InstagramUserInfoFetcherInterface
{
    /**
     * Fetches user information from the Instagram API.
     *
     * This method retrieves a specific piece of user information (e.g., username, follower count)
     * for a given Instagram user ID. It utilizes an access token from a SpecialUser model.
     *
     * @param SpecialUser $user The authenticated special user requesting the information.
     * @param string $userId The Instagram user ID of the target user.
     * @return array An associative array containing the requested field(s).
     *
     * @throws InvalidUserInfoTypeException If the requested field is not supported.
     *
     * @example
     * ```php
     * $fetcher = new UsernameFetcherService();
     * $userInfo = $fetcher->fetchUserInfo($specialUser, '1251542896102548');
     * echo "Username: " . $userInfo['username'];
     * ```
     */
    public function fetchUserInfo(SpecialUser $user, string $userId): array;
}
