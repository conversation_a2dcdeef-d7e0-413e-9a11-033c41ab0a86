<?php

namespace App\Http\Middleware\Webservice;

use Closure;
use Illuminate\Http\Request;

class CloudflareProxies
{
    public function handle(Request $request, Closure $next)
    {
        // Use Cloudflare's CF-Connecting-IP as the request IP
        if ($request->headers->has('CF-Connecting-IP')) {
            $request->server->set('REMOTE_ADDR', $request->header('CF-Connecting-IP'));
        }

        return $next($request);
    }
}
