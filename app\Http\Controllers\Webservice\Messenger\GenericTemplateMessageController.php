<?php

namespace App\Http\Controllers\Webservice\Messenger;

use App\Exceptions\Webservice\InvalidApiTokenException;
use App\Exceptions\Webservice\InvalidEndpointException;
use App\Exceptions\Webservice\InvalidMessageTypeException;
use App\Http\Controllers\Webservice\Abstract\AbstractMessageController;
use App\Services\Webservice\MessageService\MessageService;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class GenericTemplateMessageController extends AbstractMessageController
{
	protected MessageService $messageService;

	public function __construct(MessageService $messageService)
	{
		$this->messageService = $messageService;
	}

	/**
	 * Sends a text message.
	 *
	 * @param Request $request
	 * @return JsonResponse
	 * @throws InvalidMessageTypeException
	 * @throws InvalidEndpointException
	 * @throws InvalidApiTokenException
	 * @throws ValidationException
	 */
	public function send(Request $request): JsonResponse
	{
		$specialUser = $this->validateRequest($request);

		$validator = Validator::make($request->all(), [
			'receiver_id'                  => 'required|string',
			'elements'                     => 'required|array',
			'elements.*.title'             => 'required|string',
			'elements.*.image_url'         => 'nullable|url',
			'elements.*.subtitle'          => 'nullable|string',
			'elements.*.buttons'           => 'nullable|array',
            'elements.*.buttons.*.type' => ['required', 'string', Rule::in(['web_url', 'postback'])],
			'elements.*.buttons.*.title'   => 'required|string',
			'elements.*.buttons.*.url'     => 'nullable|url',
			'elements.*.buttons.*.payload' => 'nullable|string',
		]);

		// Add custom validation logic for conditional rules
		$validator->after(function ($validator) use ($request) {
			$elements = $request->input('elements', []);

			foreach ($elements as $elementIndex => $element) {
				$buttons = $element['buttons'];

				foreach ($buttons as $buttonIndex => $button) {
					if (isset($button['type'])) {
						if ($button['type'] === 'web_url' && empty($button['url']))
							$validator->errors()->add("elements.$elementIndex.buttons.$buttonIndex.url", 'The URL is required when the button type is web_url.');

						if ($button['type'] === 'postback' && empty($button['payload']))
							$validator->errors()->add("elements.$elementIndex.buttons.$buttonIndex.payload", 'The payload is required when the button type is postback.');
					}
				}
			}
		});

		if ($validator->fails()) {
			return response()->json([
				'success' => false,
				'message' => 'Validation failed',
				'errors'  => $validator->errors(),
			], 422);
		}

		$messageId = $this->messageService->sendMessage(
			$specialUser,
			$validator->validated(),
			'generic-template',
		);

		return response()->json([
			'success'    => true,
			'message'    => 'Message sent successfully',
			'message_id' => $messageId,
		]);
	}
}
