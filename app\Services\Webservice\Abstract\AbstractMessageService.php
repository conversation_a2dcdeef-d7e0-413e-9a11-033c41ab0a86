<?php

namespace App\Services\Webservice\Abstract;

use App\Models\Webservice\SpecialUser;
use App\Contracts\Webservice\MessageSenderInterface;
use Exception;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Arr;

abstract class AbstractMessageService implements MessageSenderInterface
{
    protected string $attachmentType;

    /**
     * Sends a message using the Instagram API.
     *
     * @param SpecialUser $user
     * @param array $messageData
     * @return string|null The message_id returned by the Instagram API, or null
     * @throws Exception If the request fails
     */
    public function sendMessage(SpecialUser $user, array $messageData): ?string
    {
        $receiverId = $messageData['receiver_id'];
        $messageContent = $this->getMessageContent($messageData);

        $payload = $this->preparePayload($receiverId, $messageContent);

        $response = $this->sendRequest($user, $payload);

        return $this->extractMessageId($response);
    }

    /**
     * Gets the message content based on the message type.
     *
     * @param array $messageData
     * @return array
     */
    abstract protected function getMessageContent(array $messageData): array;

    /**
     * Prepares the payload for the Instagram API request.
     *
     * @param string $receiverId
     * @param array $messageContent
     * @return array
     */
    protected function preparePayload(string $receiverId, array $messageContent): array
    {
	    if (isset($messageContent['quick_replies'])) {
		    return [
			    'recipient'      => [
				    'id' => $receiverId,
			    ],
			    'messaging_type' => 'response',
			    'message'        => $messageContent,
		    ];
	    }

        return [
            'recipient' => [
                'id' => $receiverId,
            ],
            'message' => $messageContent,
        ];
    }

    /**
     * Sends the HTTP POST request to the Instagram API.
     *
     * @param SpecialUser $user
     * @param array $payload
     * @return Response
     * @throws ConnectionException
     */
    protected function sendRequest(SpecialUser $user, array $payload): Response
    {
        return Http::withHeaders([
            'Authorization' => 'Bearer ' . $user->access_token,
            'Content-Type' => 'application/json',
        ])->post('https://graph.instagram.com/v21.0/me/messages', $payload);
    }

    /**
     * Extracts the message ID from the response.
     *
     * @param Response $response
     * @return string|null
     */
    protected function extractMessageId(Response $response): ?string
    {
        $responseBody = $response->json();
        return $responseBody['message_id'] ?? null;
    }
}
