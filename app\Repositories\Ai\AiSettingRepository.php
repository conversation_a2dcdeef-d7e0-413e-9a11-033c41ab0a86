<?php

namespace App\Repositories\Ai;

use App\Models\Ai\AiSetting;
use App\Repositories\Interfaces\AiSettingRepositoryInterface as AiSettingRepoInterface;

class AiSettingRepository implements AiSettingRepoInterface
{
    /**
     * Checks if AI settings exist for the given post_id.
     *
     * @param string $postId
     * @return bool
     */
    public function existsForPostId(string $postId): bool
    {
        return AiSetting::where('post_id', $postId)->exists();
    }
}
