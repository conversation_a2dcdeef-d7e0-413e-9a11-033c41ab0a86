<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Lara<PERSON>\Scout\Searchable;
use Illuminate\Database\Eloquent\SoftDeletes;

class Customer extends Model
{
    use HasFactory,Searchable,SoftDeletes;
    public function getScoutKey(): mixed
    {
        return $this->id;
    }
    public function getScoutKeyName(): mixed
    {
        return 'id';
    }
    public function toSearchableArray()
    {
       return [
           'id' => $this->getKey(),
           'name' => $this->name,
           'username' => $this->username,
           'customer_id'=> $this->customer_id,
           'created_at'=>$this->created_at->timestamp
        ];
    }
    public function orders(): HasMany {
        return $this->hasMany(order::class, 'customer_id','id');
    }
    public function orderAttributes(): HasMany {
        return $this->hasMany(orderAttribute::class, 'customer_id','id');
    }
}
