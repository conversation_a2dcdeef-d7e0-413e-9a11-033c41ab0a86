<?php

require __DIR__ . '/../../vendor/autoload.php';

use phpseclib3\Crypt\AES;
use phpseclib3\Crypt\PublicKeyLoader;
use phpseclib3\Crypt\Random;

function encryptData($data)
{
    $publicKeyString = config('app.public_key');  // The recipient's public RSA key
    // 1. Generate a random AES key
    $aesKey = Random::string(32);  // 256-bit key for AES-256

    // 2. Encrypt the data using the AES key
    $cipher = new AES('cbc');
    $cipher->setKey($aesKey);
    $iv = Random::string($cipher->getBlockLength() >> 3);
    $cipher->setIV($iv);
    $encryptedData = $cipher->encrypt($data);
    //echo $encryptedData . PHP_EOL;

    // 3. Encrypt the AES key using RSA with the recipient's public key
    $rsaPublic = PublicKeyLoader::load($publicKeyString);
    $encryptedAesKey = $rsaPublic->encrypt($aesKey);

    $decData[] = base64_encode($encryptedData);
    $decData[] = base64_encode($encryptedAesKey);
    $decData[] = base64_encode($iv);
    return $decData;

}

function decryptData($encryptedData, $encryptedAesKey, $iv)
{

    $privateKeyString = config('app.cyber_key');
    $password = config('app.private_key_password');

    // 4. Decrypt the AES key using the recipient's RSA private key
    $rsaPrivate = PublicKeyLoader::load($privateKeyString, $password);
    $decryptedAesKey = $rsaPrivate->decrypt($encryptedAesKey);

    // 5. Decrypt the data using the decrypted AES key
    $cipher = new AES('cbc');
    $cipher->setKey($decryptedAesKey);
    //$iv = Random::string($cipher->getBlockLength() >> 3);
    $cipher->setIV($iv);
    $decryptedData = $cipher->decrypt($encryptedData);

    return $decryptedData;  // This should output your original data string

}
