<?php

namespace App\Services\GatewayService;

use App\Contracts\Gateway\PaymentGateway;
use Illuminate\Support\Facades\Http;

class ZarinpalGateway implements PaymentGateway
{
    private $APIKey;
    public function __construct(string $apiKey) {
        $this->APIKey = $apiKey;
    }
    public function processPayment(int $amount,array $data) {
        $payload = [
            "amount"=> $amount,
            "merchant_id"=> $this->APIKey,
            "callback_url"=> $data->callback_url,
            "description"=> $data->description,
            "metadata"=> [
                "mobile"=> $data->phone,
                "email"=> $data->email
            ]
        ];
        $response = Http::withHeaders([
        ])->post('https://api.zarinpal.com/pg/v4/payment/request.json', $payload);
        return $response;

    }
    public function verifyPayment(array $data) {
        $payload = [
            "merchant_id"=> $this->APIKey,
//            "amount"=> $amount,
            "authority"=>$data['refId']
        ];
        $response = Http::withHeaders([
            'Authorization' => $this->APIKey
        ])->post('https://api.zarinpal.com/pg/v4/payment/verify.json', $payload);
        return $response;

    }

}
