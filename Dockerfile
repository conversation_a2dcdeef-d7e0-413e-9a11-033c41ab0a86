FROM dunglas/frankenphp

RUN install-php-extensions \
    pcntl zip pdo_mysql pdo_pgsql http ftp gd mongodb-stable redis

WORKDIR /app

COPY . .

RUN php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
RUN php composer-setup.php
RUN php -r "unlink('composer-setup.php');"
RUN mv composer.phar /usr/local/bin/composer

RUN /usr/local/bin/composer install

# This package is for jalali datetime
RUN /usr/local/bin/composer require hekmatinasser/verta

EXPOSE 8081
