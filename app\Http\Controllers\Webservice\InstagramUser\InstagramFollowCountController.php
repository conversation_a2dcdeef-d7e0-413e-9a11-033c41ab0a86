<?php

namespace App\Http\Controllers\Webservice\InstagramUser;

use App\Exceptions\Webservice\InvalidApiTokenException;
use App\Exceptions\Webservice\InvalidEndpointException;
use App\Exceptions\Webservice\InvalidUserInfoTypeException;
use App\Http\Controllers\Webservice\Abstract\AbstractInstagramUserController;
use App\Services\Webservice\InstagramUserService\InstagramUserInfoService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class InstagramFollowCountController extends AbstractInstagramUserController
{
    protected InstagramUserInfoService $userInfoService;

    public function __construct(InstagramUserInfoService $userInfoService)
    {
        $this->userInfoService = $userInfoService;
    }

    /**
     * Fetches the follower count of a user.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws InvalidUserInfoTypeException
     * @throws InvalidEndpointException
     * @throws InvalidApiTokenException
     * @throws ValidationException
     */
    public function fetchInfo(Request $request): JsonResponse
    {
        $specialUser = $this->validateRequest($request);

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors'  => $validator->errors(),
            ], 422);
        }

        $data = $this->userInfoService->getUserInfo(
            $specialUser,
            $validator->validated()['user_id'],
            ['follow_count']
        );

        return response()->json([
            'success' => true,
            'data'    => [
                'follower_count' => $data['follower_count'] ?? 0,
            ],
        ], 200);
    }
}
