# Webhook Timing Debug Guide

This guide explains how to use the webhook timing feature to debug performance issues in the Instagram webhook handler.

## Overview

The webhook timing feature logs detailed execution times for each operation in the `InstagramWebhookController::handle()` method. This helps identify which operations are taking the most time and causing performance bottlenecks.

## How to Enable Timing Logs

To enable timing logs, add `debug=true` as a parameter to your webhook request:

### For POST requests:
```json
{
  "debug": "true",
  "entry": [
    {
      "id": "your_page_id",
      "changes": [...]
    }
  ]
}
```

### For GET requests:
```
https://yoursite.com/api/instagram-webhook?debug=true&other_params=...
```

## What Gets Logged

When debug mode is enabled, the following operations are timed and logged:

1. **Overall Request Timing**
   - Total execution time from start to finish
   - Memory usage at start and peak memory usage

2. **Individual Operations**
   - `isIdeaPage` - Check if request is for idea page
   - `isInstagramWebhook` - Validate Instagram webhook and database lookup
   - `mustIgnoreWebhook` - Check if webhook should be ignored
   - `isManyMessageUser` - Database lookup for ManyMessage users
   - `sendToManyMessageService` - HTTP request to external service
   - `isSpecialUser` - Database lookup for special users
   - Controller forwarding operations

3. **Database Queries**
   - Page lookup by `page_user_id`
   - ManymessageAgent existence check
   - SpecialUser Instagram ID check

4. **HTTP Requests**
   - ManyMessage service calls with detailed timing

## Log File Location

Timing logs are written to: `storage/logs/webhook_timing.log`

## Monitoring Logs in Real-time

To monitor timing logs as they happen:

```bash
tail -f storage/logs/webhook_timing.log
```

## Sample Log Output

```json
[2024-01-15 10:30:15] local.INFO: Webhook request started {"request_id":"webhook_65a4f1234567","timestamp":"2024-01-15T10:30:15.123Z","memory_usage":2097152}

[2024-01-15 10:30:15] local.INFO: Operation completed {"request_id":"webhook_65a4f1234567","operation":"isIdeaPage","execution_time_ms":0.15,"memory_usage":2097152,"timestamp":"2024-01-15T10:30:15.124Z"}

[2024-01-15 10:30:15] local.INFO: Database query - Page lookup {"page_user_id":"17841470170944577","db_time_ms":12.34,"found":"yes","timestamp":"2024-01-15T10:30:15.136Z"}

[2024-01-15 10:30:15] local.INFO: Operation completed {"request_id":"webhook_65a4f1234567","operation":"isInstagramWebhook","execution_time_ms":15.67,"memory_usage":2097152,"timestamp":"2024-01-15T10:30:15.140Z"}

[2024-01-15 10:30:15] local.INFO: Starting HTTP request to ManyMessage service {"request_id":"manymessage_65a4f1234568","url":"https://panel.manymessage.com/bot/reader.php","timestamp":"2024-01-15T10:30:15.141Z"}

[2024-01-15 10:30:15] local.INFO: HTTP request to ManyMessage completed {"request_id":"manymessage_65a4f1234568","http_time_ms":245.67,"status_code":200,"successful":true,"timestamp":"2024-01-15T10:30:15.387Z"}

[2024-01-15 10:30:15] local.INFO: Webhook request completed {"request_id":"webhook_65a4f1234567","total_execution_time_ms":267.89,"peak_memory_usage":2359296,"timestamp":"2024-01-15T10:30:15.391Z"}
```

## Performance Impact

- **When debug=false or not set**: No performance impact - timing code is completely skipped
- **When debug=true**: Minimal overhead from `microtime()` calls and logging

## Troubleshooting Common Issues

### Slow Database Queries
Look for high `db_time_ms` values in database query logs. Consider:
- Adding database indexes
- Optimizing query conditions
- Database connection issues

### Slow HTTP Requests
Look for high `http_time_ms` values in HTTP request logs. Consider:
- Network latency to external services
- External service response times
- Timeout configurations

### High Memory Usage
Monitor `memory_usage` and `peak_memory_usage` values for memory leaks or excessive memory consumption.

## Testing the Feature

You can test the timing feature using curl:

```bash
curl -X POST https://yoursite.com/api/instagram-webhook \
  -H "Content-Type: application/json" \
  -d '{
    "debug": "true",
    "entry": [
      {
        "id": "17841470170944577",
        "changes": [
          {
            "value": {
              "from": {"id": "test_user"},
              "message": {"text": "test message"}
            }
          }
        ]
      }
    ]
  }'
```

Then check the timing logs:
```bash
tail -f storage/logs/webhook_timing.log
```
