<?php

namespace App\Services\BasalamService;

use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class TextService
{
	public function processTextBaSalam(string $text, string $endpoint): ?string
	{
		try {
			$response = Http::timeout(20)->post($endpoint, ['text' => $text]);

			if ($response->successful())
				return $response->body();

			return null;
		} catch (\Exception $e) {
			return null;
		}
	}

	public function processTextTorob(string $text, string $endpoint): ?string
	{
		try {
			$response = Http::timeout(20)->get($endpoint, ['query' => $text]);

			if ($response->successful())
				return $response->body();

			return null;
		} catch (\Exception $e) {
			return null;
		}
	}

	public function getProductFromTextQuery(string $textQuery): ?string
	{
		try {
			$prompt = Str::of('در متن زیر اگر اسم محصول وجود دارد فقط اسم اولین محصول را برگردان و در غیر اینصورت کلمه‌ی false را برگردان:');
			$prompt = $prompt->append("\n\n")->append($textQuery)->value();
			$baseUrl = config('services.open_ai.base_url');
			$headers = [
				'Content-Type'  => 'application/json',
				'Authorization' => 'Bearer '.config('services.open_ai.api_key'),
			];
			$data = [
				'model'      => 'gpt-4o',
				'max_tokens' => 2000,
				'messages'   => [[
					'role'    => 'user',
					'content' => $prompt,
				]],
			];
			$response = Http::timeout(20)
				->withHeaders($headers)
				->post($baseUrl.'/completions', $data);
			$result = null;

			if ($response->successful()) {
				$result = $response->json('choices.0.message.content');
				$result = $result !== 'false' ? $result : null;
			}

			return $result;
		} catch (ConnectionException $e) {
			Log::error("***************** OPEN AI ******************");
			Log::error($e->getMessage());

			return null;
		}
	}
}
