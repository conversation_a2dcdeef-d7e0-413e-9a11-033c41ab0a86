<?php

namespace App\Services\BasalamService\Webhook;

class WebhookExtractor
{
    public function extract(array $data): ?array
    {
        if (isset($data['object']) && $data['object'] === 'instagram') {
            if (isset($data['entry']) && is_array($data['entry'])) {
                foreach ($data['entry'] as $entry) {
                    if (isset($entry['messaging']) && is_array($entry['messaging'])) {
                        foreach ($entry['messaging'] as $messageData) {
                            if (isset($messageData['message']['attachments']) && is_array($messageData['message']['attachments'])) {
                                if (count($messageData['message']['attachments']) > 1) {
                                    return ['type' => 'images', 'url' => $messageData['message']['attachments'][0]['payload']['url']];
                                }
                                foreach ($messageData['message']['attachments'] as $attachment) {
                                    if (isset($attachment['type']) && isset($attachment['payload']['url'])) {
                                        switch ($attachment['type']) {
                                            case 'image':
                                                return ['type' => 'image', 'url' => $attachment['payload']['url']];
                                            case 'share':
                                                return ['type' => 'share', 'url' => $attachment['payload']['url']];
                                            case 'audio':
                                                return ['type' => 'audio', 'url' => $attachment['payload']['url']];
                                            case 'video':
                                                return ['type' => 'video', 'url' => $attachment['payload']['url']];
                                            case 'ig_reel':
                                                return ['type' => 'reels', 'url' => $attachment['payload']['url']];
                                        }
                                    }
                                }
                            }
                            if (isset($messageData['message']['text'])) {
                                return ['type' => 'text', 'text' => $messageData['message']['text']];
                            }
                        }
                    }
                }
            }
        }

        return null;
    }
}
