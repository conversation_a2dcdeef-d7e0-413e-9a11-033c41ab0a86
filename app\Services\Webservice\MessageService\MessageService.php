<?php

namespace App\Services\Webservice\MessageService;

use App\Models\Webservice\SpecialUser;
use App\Contracts\Webservice\MessageSenderInterface;
use App\Exceptions\Webservice\InvalidMessageTypeException;

class MessageService
{
    /**
     * An Interface array to allow the class MessageService to dynamically
     * manage the different types of message senders for example @class TextMessageService
     * or @class MediaMessageService etc.
     *
     * @var MessageSenderInterface[]
     */
    protected array $messageSenders;

    /**
     * MessageService constructor.
     *
     * @param array $messageSenders
     */
    public function __construct(array $messageSenders)
    {
        $this->messageSenders = $messageSenders;
    }

    /**
     * Sends a message to the special user's endpoint based on message type.
     *
     * @param SpecialUser $user
     * @param array $messageData
     * @param string $messageType
     * @throws InvalidMessageTypeException
     */
    public function sendMessage(SpecialUser $user, array $messageData, string $messageType)
    {
        if (!isset($this->messageSenders[$messageType])) {
            throw new InvalidMessageTypeException(messageType: $messageType);
        }

        $messageSender = $this->messageSenders[$messageType];

        return $messageSender->sendMessage($user, $messageData);
    }
}
