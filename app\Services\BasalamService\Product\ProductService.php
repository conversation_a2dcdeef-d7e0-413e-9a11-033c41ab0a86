<?php

namespace App\Services\BasalamService\Product;

use App\Models\Basalam\BasalamProduct;
use App\Repositories\BasalamRepository\BasalamProductRepository;

/**
 *  Class ProductService
 *
 *  This Service is responsible for handling product-related operations in the Basalam application.
 *
 *  It interacts with the product repository to retrieve product link & details and formats those details
 *  into a user-friendly description message, primarily for displaying in Instagram Direct.
 */
class ProductService
{

    /**
     * Repository instance to interact with product data.
     *
     * @var BasalamProductRepository
     */
    protected BasalamProductRepository $productRepository;

    public function __construct(BasalamProductRepository $productRepository)
    {
        $this->productRepository = $productRepository;
    }

    /**
     * Retrieves and formats the product description into a message string.
     *
     * The formatted message is typically used for sending in the messaging platform of Instagram.
     *
     * @param int $product_id The unique identifier for the product.
     * @return string The formatted product description message.
     */
    public function getDescriptionMessage(int $product_id): string
    {
        $product = $this->productRepository->getProduct($product_id);

        $message = $this->prepareDescriptionMessage($product);

        return $message;
    }

    /**
     * Retrieves Product Link to basalam page.
     *
     * This link also contains utm_source and utm_medium.
     *
     * @param int $product_id
     * @return string
     */
    public function getProductLink(int $product_id): string
    {
        $product = $this->productRepository->getProduct($product_id);

        return "https://basalam.com/{$product->vendor_identifier}/product/{$product_id}?utm_source=instagram&utm_medium=directam";
    }

    /**
     * Prepares a formatted description message for a given product.
     *
     * This method takes a BasalamProduct object and generates a string containing formatted
     * product information. The message includes product name, price, rating, stock, availability,
     * and other attributes, with fallback values for missing or null data.
     *
     * @param BasalamProduct $product The product object containing the product data.
     * @return string The formatted message string.
     */
    protected function prepareDescriptionMessage(BasalamProduct $product): string
    {

        $message = "🛒 *اطلاعات کالا*\n";

        $message .= "📝 *نام کالا:* " . ($product->name ?? 'نامشخص') . "\n";

        $message .= "📋 *توضیحات:* " . ($product->description ?? 'توضیحات موجود نیست') . "\n";

        $message .= "💰 *قیمت:* " . number_format($product->price ?? 0) . " تومان\n";

        $message .= "⭐ *میانگین امتیاز:* " . ($product->average_rating ?? 'امتیاز ثبت نشده') . "\n";

        $message .= "💬 *تعداد نظرات:* " . ($product->rating_count ?? 0) . "\n";

        $message .= "🛍️ *وضعیت:* " . ($product->status ?? 'نامشخص') . "\n";

        $message .= "👤 *مالک:* " . ($product->owner_name ?? 'نامشخص') . "\n";

        $message .= "🏙️ *شهر مالک:* " . ($product->owner_city ?? 'نامشخص') . "\n";

        $message .= "📦 *تعداد موجود در انبار:* " . ($product->stock ?? 0) . "\n";

        $message .= "⚖️ *وزن:* " . ($product->weight ?? 0) . " گرم\n";

        $message .= ($product->has_delivery ? "🚚 *ارسال به سراسر کشور*" : "🚫 *ارسال محدود*") . "\n";

        $message .= ($product->is_available ? "✔️ *کالا در دسترس است*" : "❌ *کالا در دسترس نیست*") . "\n";

        $message .= ($product->can_add_to_cart ? "🛒 *امکان اضافه به سبد خرید*" : "❌ *امکان اضافه به سبد خرید وجود ندارد*") . "\n";

        $message .= "⏳ *زمان آماده سازی:* " . ($product->preparation_days ?? 0) . " روز\n";

        return $message;
    }
}
