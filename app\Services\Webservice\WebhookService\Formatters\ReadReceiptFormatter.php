<?php

namespace App\Services\Webservice\WebhookService\Formatters;

use App\Contracts\Webservice\WebhookFormatter;

class ReadReceiptFormatter implements WebhookFormatter
{
    public function format(array $message): array
    {
        return [
            'sender'       => $message['sender']['id'],
            'receiver'     => $message['recipient']['id'],
            'message_type' => 'read',
            'seen' => [
                'message_id' => $message['read']['mid'],
            ],
            'timestamp' => gmdate('Y-m-d\TH:i:s\Z', intdiv((int)$message['timestamp'], 1000)),
            'is_admin'  => false,
        ];
    }
}
