<?php

namespace App\Contracts\SMS;

use Exception;

interface SmsInterface
{
    /**
     * Sends a generic SMS message.
     *
     * @param string $to
     * @param string $message
     * @return array
     * @throws Exception
     */
    public function sendSms(string $to, string $message): array;

    /**
     * Sends a transaction success SMS.
     *
     * @param string $to
     * @param string $pageUsername
     * @param string $time
     * @param string $date
     * @param string $trackingCode
     * @param string $amount
     * @return array
     * @throws Exception
     */
    public function sendTransactionSuccessSms(
        string $to,
        string $pageUsername,
        string $time,
        string $date,
        string $trackingCode,
        string $amount
    ): array;
}
