<?php

namespace App\Console\Commands\Basalam;

use App\Repositories\BasalamRepository\BasalamProductRepository as Repository;
use Illuminate\Console\Command;

class DeleteOldBasalamProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'basalam:delete-old-products';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Basalam products older than 2 days';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $deletedCount = Repository::deleteExpiredProducts(2);

        $this->info("Deleted {$deletedCount} products older than 2 days.");
    }
}
