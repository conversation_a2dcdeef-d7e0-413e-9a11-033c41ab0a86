<?php

namespace App\Providers;

use App\Contracts\SMS\SmsInterface;
use App\Repositories\Ai\AiSettingRepository;
use App\Repositories\Interfaces\AiSettingRepositoryInterface;
use App\Services\SmsService\SapakService;
use App\Services\Webservice\InstagramUserService\FollowerCountFetcherService;
use App\Services\Webservice\InstagramUserService\FollowerStatusFetcherService;
use App\Services\Webservice\InstagramUserService\FollowingStatusFetcherService;
use App\Services\Webservice\InstagramUserService\InstagramUserInfoService;
use App\Services\Webservice\InstagramUserService\UsernameFetcherService;
use App\Services\Webservice\MessageService\AudioMessageService;
use App\Services\Webservice\MessageService\ButtonTextMessageService;
use App\Services\Webservice\MessageService\GenericTemplateMessageService;
use App\Services\Webservice\MessageService\ImageMessageService;
use App\Services\Webservice\MessageService\MessageService;
use App\Services\Webservice\MessageService\QuickReplyMessageService;
use App\Services\Webservice\MessageService\TextMessageService;
use App\Services\Webservice\MessageService\VideoMessageService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(SmsInterface::class, function ($app) {
            return new SapakService();
        });

        $this->app->bind(MessageService::class, function ($app) {
	        return new MessageService([
		        'text'             => $app->make(TextMessageService::class),
		        'video'            => $app->make(VideoMessageService::class),
		        'image'            => $app->make(ImageMessageService::class),
		        'audio'            => $app->make(AudioMessageService::class),
		        'button-text'      => $app->make(ButtonTextMessageService::class),
		        'quick-reply'      => $app->make(QuickReplyMessageService::class),
		        'generic-template' => $app->make(GenericTemplateMessageService::class),
	        ]);
        });

        $this->app->bind(InstagramUserInfoService::class, function ($app) {
            return new InstagramUserInfoService([
                'username'     => $app->make(UsernameFetcherService::class),
                'follow_count' => $app->make(FollowerCountFetcherService::class),
                'is_follower'  => $app->make(FollowerStatusFetcherService::class),
                'is_following' => $app->make(FollowingStatusFetcherService::class),
            ]);
        });

        $this->app->bind(AiSettingRepositoryInterface::class, AiSettingRepository::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
