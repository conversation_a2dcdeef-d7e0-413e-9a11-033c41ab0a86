<?php

namespace App\Services;

use App\Models\CrmAction;
use App\Models\CrmTrack;
use App\Models\Page;
use App\Models\Transaction;
use App\Models\User;
use GuzzleHttp\Promise\PromiseInterface;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class CrmApiService
{
//	private const API_BASE_URL = 'http://localhost:8000/api/webhook/contact';
	private const API_BASE_URL = 'https://3fe32w.manymessage.com/api/webhook/contact';
	private const API_KEY = 'AWajaojawojdoajdawhsbnx2&&888q1!!!';

	private function post(string $uri, array $body = []): PromiseInterface|Response
	{
		try {
			return Http::baseUrl(self::API_BASE_URL)
				->acceptJson()
				->withHeaders(['Authorization' => self::API_KEY])
				->post($uri, $body);
		} catch (\Exception $e) {
			return Http::response([
				'is_processed' => false,
				'message' => "Request failed with message {$e->getMessage()}",
			], 503);
		}
	}

	public function sendUserData(
		User      $user,
		CrmAction $crmAction = null,
		CrmTrack  $crmTrack = null,
	): void
	{
		if ($crmAction === null)
			$crmAction = CrmAction::where('name', CrmAction::USER_REGISTER)->first();

		if ($crmTrack === null) {
			// This section will be executed when sending data for the first time

			// Create new tracking for user registration
			if(!$crmAction){
				return;
			}
			$crmTrack = CrmTrack::firstOrCreate([
				'crm_action_id' => $crmAction->id ,
				'user_id' => $user->id,
				'page_id' => null,
				'transaction_id' => null,
			]);

			// If user has successful track don't send the request
			if ($crmTrack->is_processed)
				return;

			$response = $this->post('/register', compact('user'));
			// Update tracking "is_processed" column to true
			// if response is successful and CRM handled the request
			if ($response->successful() && $response->json('is_processed'))
				$crmTrack->update(['is_processed' => true]);

			// Create temporary log file
			$this->log([
				'source' => 'sendUserData',
				'params' => $user,
				'response' => $response->json(),
			]);
		} else {
			// This section will be executed when resending data from command
			$response = $this->post('/register', compact('user'));
			// Update tracking "is_processed" column to true
			// if response is successful and CRM handled the request
			if ($response->successful() && $response->json('is_processed'))
				$crmTrack->update(['is_processed' => true]);

			// Create temporary log file
			$this->log([
				'source' => 'sendUserData',
				'params' => $user,
				'response' => $response->json(),
			]);
		}
	}

	public function sendPageData(
		Page      $page,
		int       $days,
		CrmAction $crmAction = null,
		CrmTrack  $crmTrack = null,
	): void
	{
		if ($crmAction === null)
			$crmAction = match ($days) {
				0 => CrmAction::where('name', CrmAction::PAGE_EXPIRED)->first(),
				2 => CrmAction::where('name', CrmAction::PAGE_2_DAYS_TO_EXPIRE)->first(),
				7 => CrmAction::where('name', CrmAction::PAGE_7_DAYS_TO_EXPIRE)->first(),
			};

		if ($crmTrack === null) {
			// This section will be executed when sending data for the first time

			// Find or create new tracking for page expiration
			$crmTrack = CrmTrack::firstOrCreate([
				'crm_action_id' => $crmAction->id,
				'user_id' => $page->user->id,
				'page_id' => $page->id,
				'transaction_id' => $page->transaction->id,
			]);

			// If page has successful track don't send the request
			if ($crmTrack->is_processed)
				return;

			// Send page data to the CRM
			$page->load('user');
			$response = $this->post('/expire', [
				'page' => $page,
				'remaining_days' => $days,
			]);

			// Update tracking "is_processed" column to true
			// if response is successful and CRM handled the request
			if ($response->successful() && $response->json('is_processed'))
				$crmTrack->update(['is_processed' => true]);

			// Create temporary log file
			$this->log([
				'source' => 'sendPageData',
				'params' => [
					'page' => $page,
					'remaining_days' => $days,
				],
				'response' => $response->json(),
			]);
		} else {
			// This section will be executed when resending data from command

			// Send page data to the CRM
			$response = $this->post('/expire', [
				'page' => $page,
				'remaining_days' => $days,
			]);

			// Update tracking "is_processed" column to true
			// if response is successful and CRM handled the request
			if ($response->successful() && $response->json('is_processed'))
				$crmTrack->update(['is_processed' => true]);

			// Create temporary log file
			$this->log([
				'source' => 'sendPageData',
				'params' => [
					'page' => $page,
					'remaining_days' => $days,
				],
				'response' => $response->json(),
			]);
		}
	}

	public function sendTransactionData(
		Transaction $transaction,
		CrmAction   $crmAction,
		CrmTrack    $crmTrack = null,
	): void
	{
		if ($crmTrack === null) {
			// This section will be executed when sending data for the first time

			// Create new tracking for transaction
			$crmTrack = CrmTrack::firstOrCreate([
				'crm_action_id' => $crmAction->id,
				'user_id' => $transaction->user_id,
				'page_id' => $transaction->page_id,
				'transaction_id' => $transaction->id,
			]);

			// If transaction has successful track don't send the request
			if ($crmTrack->is_processed)
				return;

			// Send transaction data to the CRM
			$transaction->load('user');
			$response = $this->post('/transaction', compact('transaction'));

			// Update tracking "is_processed" column to true
			// if response is successful and CRM handled the request
			if ($response->successful() && $response->json('is_processed'))
				$crmTrack->update(['is_processed' => true]);

			// Create temporary log file
			$this->log([
				'source' => 'sendTransactionData',
				'params' => $transaction,
				'response' => $response->json(),
			]);
		} else {
			// This section will be executed when resending data from command

			// Send transaction data to the CRM
			$transaction->load('user');
			$response = $this->post('/transaction', compact('transaction'));

			// Update tracking "is_processed" column to true
			// if response is successful and CRM handled the request
			if ($response->successful() && $response->json('is_processed'))
				$crmTrack->update(['is_processed' => true]);

			// Create temporary log file
			$this->log([
				'source' => 'sendTransactionData',
				'params' => $transaction,
				'response' => $response->json(),
			]);
		}

	}

	private function log($input): void
	{
		$now = now();
		Storage::put("log_{$now->format('Y.m.d_H.i.s')}.json", json_encode($input));
	}
}
