<?php

namespace App\Models\Ai;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class AiSetting extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'ai_settings';

    protected $fillable = [
        'user_id',
        'friendly_tone',
        'bot_character',
        'post_id',
        'custom_prompt',
        'post_description',
        'ai_driver',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
