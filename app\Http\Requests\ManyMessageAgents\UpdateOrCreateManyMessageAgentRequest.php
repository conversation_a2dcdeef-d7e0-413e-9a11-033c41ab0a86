<?php

namespace App\Http\Requests\ManyMessageAgents;

use Illuminate\Foundation\Http\FormRequest;

class UpdateOrCreateManyMessageAgentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => 'string|required|email|max:255',
            'insta_id' => 'string|sometimes|max:255',
        ];
    }
}
