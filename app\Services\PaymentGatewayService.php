<?php
namespace App\Services;
use App\Services\GatewayService\PaystarGateway;
use App\Services\GatewayService\PaystarCardGateway;
use App\Services\GatewayService\SnappGateway;
class PaymentGatewayService {
    private $paymentType;
    private $api_key;
    private $data;
    private $gateway;

    public function __construct(string $paymentType,string $api_key,array $data=[]) {
        $this->paymentType = $paymentType;
        $this->api_key = $api_key;
        $this->data = $data;
        if ($this->paymentType === 'zarinpal') {
        }else if ($this->paymentType === 'payping') {
        }else if ($this->paymentType ==='paystar'){
            $gateway = new PaystarGateway($this->api_key);
        }else if($this->paymentType === 'snapp'){
            $gateway = new SnappGateway(
                $this->data['user'],
                $this->data['pass'],
                $this->data['clientId'],
                $this->api_key
            );
        }else if($this->paymentType ==='paystarCard'){
            $gateway = new PaystarCardGateway($this->api_key);
        }
        $this->gateway = $gateway;
    }
    public function process( int $amount, array $data) {
        $processor = new PaymentProcessorService($this->gateway);
        return $processor->processPayment($amount,$data);
    }
    public function verify(array $data) {
        $processor = new PaymentProcessorService($this->gateway);
        return $processor->verifyPayment($data);
    }
    public function cancel(array $data) {
        $processor = new PaymentProcessorService($this->gateway);
        return $processor->cancelPayment($data);
    }
    public function getlink(string $tag){
        return 'https://dmplus.manymessage.com/api/redirectPayment?tag='.$tag.'&paymentType='.$this->paymentType;
    }
}
