<?php

namespace App\Providers;

use App\Services\Webservice\UserService\UserThrottleService;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\ServiceProvider;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        RateLimiter::for('webservice_throttle', function (Request $request) {
            $ip = $request->ip();
            $apiToken = $request->header('api-token'); // Extract API token from header

            if (!$apiToken) {
                // Default throttling for requests without API tokens
                return Limit::perMinute(20)->by($ip);
            }

            // Fetch valid endpoints for the user
            $validEndpoints = UserThrottleService::getValidEndpointsByApiToken($apiToken);

            if ($validEndpoints && in_array($ip, $validEndpoints)) {
                // Special users with valid IP: 15 requests per second
                return Limit::perSecond(15)->by($ip);
            }

            // Default throttling: 20 requests per minute
            return Limit::perMinute(20)->by($ip);

        });
    }
}
