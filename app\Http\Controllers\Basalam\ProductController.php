<?php

namespace App\Http\Controllers\Basalam;

use App\Http\Controllers\Controller;
use App\Repositories\BasalamRepository\BasalamProductRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;

class ProductController extends Controller
{
    protected BasalamProductRepository $productRepository;

    public function __construct(BasalamProductRepository $productRepository)
    {
        $this->productRepository = $productRepository;
    }


	public function store(Request $request): JsonResponse
	{
		try {
			$validator = Validator::make($request->all(), [
				'image' => 'required_without_all:text|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
				'text'  => 'required_without_all:image|string',
			]);

			if ($validator->fails()) {
				return response()->json(['error' => 'Invalid input.'], 422);
			}

			$headers = [
				'User-Agent' => 'directam.ir',
				'Accept'     => 'application/json',
			];

			$hasImage = $request->hasFile('image');
			if ($hasImage) {
				$image = $request->file('image');
				$response = Http::timeout(20)
				->withHeaders($headers)
					->attach('file', file_get_contents($image->getRealPath()), 'test.jpg')
					->post('https://search.basalam.com/ai-engine/api/v1.0/image/search');
			} else {
				$text = $request->input('text');
				$response = Http::timeout(20)
				->withHeaders($headers)
					->get('https://search.basalam.com/ai-engine/api/v2.0/product/search', [
						'q' => $text,
					]);
			}

			if ($response->successful()) {
				$result = $hasImage ? $response->json() : $response->json('products');
				$productDetails = $this->productRepository->saveProducts($result);

				return response()->json([
					'status'          => 'success',
					'message'         => 'Products Saved!',
					'product_details' => $productDetails,
				]);
			}

			return response()->json(['error' => 'Unable to fetch data'], 500);
		} catch (\Exception $e) {
			return response()->json(['error' => 'Unable to fetch data'], 500);
		}
	}
}
