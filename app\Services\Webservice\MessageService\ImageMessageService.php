<?php

namespace App\Services\Webservice\MessageService;

use App\Services\Webservice\Abstract\AbstractMessageService;

class ImageMessageService extends AbstractMessageService
{
    protected string $attachmentType = 'image';

    /**
     * Gets the message content for an image message.
     *
     * @param array $messageData
     * @return array
     */
    protected function getMessageContent(array $messageData): array
    {
        return [
            'attachment' => [
                'type' => $this->attachmentType,
                'payload' => [
                    'url' => $messageData['image_url'],
                ],
            ],
        ];
    }
}
