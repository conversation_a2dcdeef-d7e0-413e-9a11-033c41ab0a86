<?php

namespace App\Services\GatewayService;

use App\Contracts\Gateway\PaymentGateway;
use Illuminate\Support\Facades\Http;

class PaystarGateway implements PaymentGateway
{
    private $APIKey;
    public function __construct(string $apiKey) {
        $this->APIKey = $apiKey;
    }
    public function processPayment(int $amount,array $data) {
        $payload = [
            "amount"=> $amount,
            "order_id"=> $data['order_id'],
            "callback"=> $data['callback'],
//            "sign"=> $data['sign'],

        ];
        $response = Http::withHeaders([
            'Authorization' => 'Bearer '. $this->APIKey
        ])->post('https://core.paystar.ir/api/pardakht/create', $payload);
        return $response;

    }
    public function verifyPayment(array $data) {
        $payload = [
            "amount"=> $data['amount'],
            "ref_num"=> $data['refId'],
            "sign"=> $data['sign']
        ];
        $response = Http::withHeaders([
            'Authorization' => 'Bearer '. $this->APIKey
        ])->post('https://core.paystar.ir/api/pardakht/verify', $payload);
        return $response;

    }

}
