<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class TelegramService
{
	public static function sendForFuad(mixed $content): void
	{
		self::send(7090324663, $content);
	}

	public static function sendForHamid(mixed $content): void
	{
		self::send(129546169, $content);
	}

	private static function send(int $chatId, mixed $content): void
	{
		if (!is_string($content))
			$content = json_encode($content, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

		$token = '7030316078:AAHJdmdad71Q64lB9pbzGuvmG1ochkYYs8M';
		$url = "https://api.telegram.org/bot$token/sendMessage";
		$telegramLengthLimit = 4096;

		if (Str::length($content) <= $telegramLengthLimit) {
			Http::post($url, [
				'chat_id' => $chatId,
				'text'    => $content,
			]);

			return;
		}

		$chunks = str_split($content, $telegramLengthLimit);
		foreach ($chunks as $chunk) {
			Http::post($url, [
				'chat_id' => $chatId,
				'text'    => $chunk,
			]);
		}
	}
}
