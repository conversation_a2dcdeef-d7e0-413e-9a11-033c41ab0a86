<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentMethod extends Model
{
    use HasFactory,SoftDeletes;

    protected $table = 'payment_methods';

    protected $fillable = [
        'page_id',
        'user_id',
        'token',
        'payment_method',
        'secret',
        'data'
    ];
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(
            Product::class,
            'payment_method_products',
            'payment_method_id',
            'products_id'
        );
    }
    public function page(): BelongsTo
    {
        return $this->belongsTo(Page::class);
    }
    public function orders(): HasMany
    {
        return $this->hasMany(order::class);
    }
}
