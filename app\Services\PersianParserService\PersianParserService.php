<?php

namespace App\Services\PersianParserService;

/**
 * PersianParserService
 *
 * This service is designed to handle Persian number and text parsing.
 */
class PersianParserService
{
    /**
     * Dictionary of Persian words and their corresponding numeric values.
     *
     * @var array
     */
    protected array $dictionary = [
        'صفر' => 0,
        'یک' => 1,
        'دو' => 2,
        'سه' => 3,
        'چهار' => 4,
        'پنج' => 5,
        'شش' => 6,
        'هفت' => 7,
        'هشت' => 8,
        'نه' => 9,
        'ده' => 10,
        'یازده' => 11,
        'دوازده' => 12,
        'سیزده' => 13,
        'چهارده' => 14,
        'پانزده' => 15,
        'شانزده' => 16,
        'هفده' => 17,
        'هجده' => 18,
        'نوزده' => 19,
        'بیست' => 20,
        'سی' => 30,
        'چهل' => 40,
        'پنجاه' => 50,
        'شصت' => 60,
        'هفت<PERSON>' => 70,
        'هشتاد' => 80,
        'نود' => 90,
        'صد' => 100,
        'دویست' => 200,
        'سیصد' => 300,
        'چهارصد' => 400,
        'پانصد' => 500,
        'ششصد' => 600,
        'هفتصد' => 700,
        'هشتصد' => 800,
        'نهصد' => 900,
        'هزار' => 1000,
        'میلیون' => 1000000,
        'میلیارد' => 1000000000,
    ];

    /**
     * Parses the given string and returns its integer equivalent.
     *
     * It can:
     *  - Detect and convert Persian numbers to English integers.
     *  - Convert Persian words representing numbers (e.g., "پانصد هزار") into integer values.
     *  - Recognize and return standard English numbers as integers.
     *
     * @param string $text Input string, which may contain Persian words, Persian numbers, or English numbers.
     * @return int|null The parsed integer if conversion is successful; null if the input is invalid.
     */
    public function parseInput(string $text): ?int
    {
        if ($this->isPersianNumber($text)) {
            return $this->convertPersianToEnglishNumbers($text);
        }elseif ($this->isPersianWordsAndNumeric($text)) {
            $text = $this->processPersianStringAlongWithNumbers($text);
            return $this->wordsToNumber($text);
        } elseif ($this->isPersianWordsAndNumbers($text)) {
            $text = $this->processPersianStringAlongWithNumbers($text);
            return $this->wordsToNumber($text);
        } elseif ($this->isPersianWords($text)) {
            return $this->wordsToNumber($text);
        } elseif (is_numeric($text)) {
            return (int) $text;
        } else {
            return null;
        }
    }

    protected function isPersianWords($text): bool
    {
        return preg_match('/[\x{0600}-\x{06FF}]/u', $text);
    }

    protected function isPersianNumber($text): bool
    {
        return preg_match('/^[۰-۹]+$/u', $text);
    }

    protected function convertPersianToEnglishNumbers(string $text): string
    {
        $persian_numbers = ['0', '۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
        $english_numbers = ['0', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

        return str_replace($persian_numbers, $english_numbers, $text);
    }

    protected function wordsToNumber($text): int
    {
        $parts = explode(' ', $text);
        $number = 0;
        $temp = 0;

        foreach ($parts as $part) {
            if (isset($this->dictionary[$part])) {
                $value = $this->dictionary[$part];
                if ($value >= 1000) {
                    $temp = ($temp ?: 1) * $value;
                    $number += $temp;
                    $temp = 0;
                } elseif ($value >= 100) {
                    $temp = ($temp ?: 1) * $value;
                } else {
                    $temp += $value;
                }
            }
        }

        return $number + $temp;
    }

    protected function isPersianWordsAndNumbers($text): bool
    {
        // Check if the text contains Persian words and Persian numbers
        return preg_match('/[\x{0600}-\x{06FF}]/u', $text) && preg_match('/[۰-۹]+/u', $text);
    }

    protected function isPersianWordsAndNumeric($text): bool
    {
        // Check if the text contains Persian words (range: 0x0600-0x06FF) and numeric digits (Arabic/English numbers)
        return preg_match('/[\x{0600}-\x{06FF}]/u', $text) && preg_match('/\d+/', $text);
    }



    protected function numberToPersianWord(string $text): string
    {

        preg_match('/\d+|[۰-۹]+/', $text, $matches);

        if (isset($matches[0])) {

            $numberStr = $matches[0];

            $persianToArabic = [
                '۰' => '0', '۱' => '1', '۲' => '2', '۳' => '3', '۴' => '4',
                '۵' => '5', '۶' => '6', '۷' => '7', '۸' => '8', '۹' => '9'
            ];

            $numberStr = strtr($numberStr, $persianToArabic);

            $number = (int)$numberStr;

            $inverseDictionary = array_flip($this->dictionary);
            $words = [];

            if ($number < 20) {

                $words[] = $inverseDictionary[$number];
            } elseif ($number < 100) {

                $tens = intdiv($number, 10) * 10;
                $ones = $number % 10;
                $words[] = $inverseDictionary[$tens];
                if ($ones > 0) {
                    $words[] = $inverseDictionary[$ones];
                }
            } elseif ($number < 1000) {
                $hundreds = intdiv($number, 100);
                $remainder = $number % 100;
                $words[] = $inverseDictionary[$hundreds * 100];
                if ($remainder > 0) {
                    $words[] = $this->numberToPersianWord($remainder);
                }
            } else {
                $powers = [1000, 1000000, 1000000000];
                foreach ($powers as $power) {
                    if ($number >= $power) {
                        $quotient = intdiv($number, $power);
                        $words[] = $this->numberToPersianWord($quotient);
                        $words[] = $inverseDictionary[$power];
                        $number = $number % $power;
                    }
                }
                if ($number > 0) {
                    $words[] = $this->numberToPersianWord($number);
                }
            }

            $result = implode(' ', $words);

            preg_match('/\s*(\D+)/u', $text, $scaleMatch);
            if (isset($scaleMatch[1])) {
                $result .= ' ' . trim($scaleMatch[1]);
            }

            return $result;
        }

        return $text;
    }

    protected function processPersianStringAlongWithNumbers($inputString) {
        $words = explode(' ', $inputString);

        $editedWords = [];

        foreach ($words as $word) {
            $editedWords[] = $this->numberToPersianWord($word);
        }

        $outputString = implode(' ', $editedWords);

        return $outputString;
    }
}
