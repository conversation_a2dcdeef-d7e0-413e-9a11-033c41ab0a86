<?php

namespace App\Http\Controllers\Idea;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

/**
 * Class MainController
 *
 * This controller acts as a pass-through proxy, forwarding Instagram-related requests
 * to the Idea Project Server, where the actual request processing takes place.
 *
 * The controller does not process or modify the request data; it simply forwards it
 * to the Idea API endpoint, as defined in the application configuration.
 */
class MainController extends Controller
{
    /**
     * The API URL for forwarding requests to the Idea Project Server
     * @var string
     */
    protected string $apiUrl;

    /**
     * MainController Constructor.
     *
     * Initializes the controller and sets the API URL from the configuration file.
     */
    public function __construct()
    {
        $this->apiUrl = config('services.idea.api_url');
    }

    /**
     * Handles the incoming request and forward it to the Idea Project Server.
     *
     * This method accepts a request from the client, extracts its data, and forwards it
     * to the external API using an HTTP POST request.
     *
     * @param Request $request The incoming HTTP request containing Instagram-related data
     * @return Response The response returned by the Idea Project Server
     */
    public function handle(Request $request): Response
    {
        return Http::post($this->apiUrl, $request->all());
    }
}
