<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckApiKey
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if API key exists in the header
        if (!$request->hasHeader('x-api-key')) {
            return response()->json([
                'error' => 'API key is required',
                'message' => 'Please provide a valid API key in the x-api-key header'
            ], 401);
        }

        // Get the API key from the header
        $apiKey = $request->header('x-api-key');
        
        // Check if the API key is valid (comparing with the one in config)
        if ($apiKey !== config('app.api_key')) {
            return response()->json([
                'error' => 'Invalid API key',
                'message' => 'The provided API key is invalid'
            ], 403);
        }

        return $next($request);
    }
}